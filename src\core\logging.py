"""
Logging and Error Handling Framework

Comprehensive logging system and error handling patterns for the entire application.
"""

import sys
import traceback
from pathlib import Path
from typing import Optional, Dict, Any, Union
from enum import Enum
from dataclasses import dataclass
from loguru import logger
import structlog


class LogLevel(Enum):
    """Log levels."""
    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    SUCCESS = "SUCCESS"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ErrorCode(Enum):
    """Application error codes."""
    # Configuration errors
    CONFIG_LOAD_ERROR = "CONFIG_001"
    CONFIG_SAVE_ERROR = "CONFIG_002"
    CONFIG_VALIDATION_ERROR = "CONFIG_003"
    
    # Authentication errors
    AUTH_INVALID_API_KEY = "AUTH_001"
    AUTH_CONNECTION_FAILED = "AUTH_002"
    AUTH_ENCRYPTION_ERROR = "AUTH_003"
    
    # Provider errors
    PROVIDER_NOT_CONFIGURED = "PROV_001"
    PROVIDER_API_ERROR = "PROV_002"
    PROVIDER_RATE_LIMIT = "PROV_003"
    PROVIDER_TIMEOUT = "PROV_004"
    PROVIDER_CREATION_FAILED = "PROV_005"
    
    # Tool errors
    TOOL_VALIDATION_ERROR = "TOOL_001"
    TOOL_EXECUTION_ERROR = "TOOL_002"
    TOOL_PERMISSION_DENIED = "TOOL_003"
    TOOL_NOT_FOUND = "TOOL_004"
    
    # CLI errors
    CLI_INPUT_ERROR = "CLI_001"
    CLI_DISPLAY_ERROR = "CLI_002"
    CLI_COMMAND_ERROR = "CLI_003"
    
    # General errors
    UNKNOWN_ERROR = "GEN_001"
    NETWORK_ERROR = "GEN_002"
    FILE_SYSTEM_ERROR = "GEN_003"


@dataclass
class AppError(Exception):
    """Application-specific error with structured information."""
    code: ErrorCode
    message: str
    details: Optional[Dict[str, Any]] = None
    cause: Optional[Exception] = None
    
    def __str__(self) -> str:
        return f"[{self.code.value}] {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for logging."""
        return {
            "error_code": self.code.value,
            "message": self.message,
            "details": self.details or {},
            "cause": str(self.cause) if self.cause else None,
            "traceback": traceback.format_exc() if self.cause else None
        }


class LoggingManager:
    """Manages application logging configuration."""
    
    def __init__(self, log_dir: Optional[Path] = None, log_level: str = "INFO"):
        """Initialize logging manager.
        
        Args:
            log_dir: Directory for log files. Defaults to ~/.ai-cli-terminal/logs
            log_level: Logging level
        """
        self.log_dir = log_dir or Path.home() / ".ai-cli-terminal" / "logs"
        self.log_level = log_level
        self.setup_logging()
    
    def setup_logging(self) -> None:
        """Set up logging configuration."""
        # Ensure log directory exists
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Remove default logger
        logger.remove()
        
        # Console logging with colors
        logger.add(
            sys.stderr,
            level=self.log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            colorize=True,
            backtrace=True,
            diagnose=True
        )
        
        # File logging (detailed)
        logger.add(
            self.log_dir / "app.log",
            level="DEBUG",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="10 MB",
            retention="30 days",
            compression="gz",
            backtrace=True,
            diagnose=True
        )
        
        # Error file logging
        logger.add(
            self.log_dir / "errors.log",
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="5 MB",
            retention="60 days",
            compression="gz",
            backtrace=True,
            diagnose=True
        )
        
        # Structured logging for analysis
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
        
        logger.info(f"Logging initialized - Level: {self.log_level}, Log dir: {self.log_dir}")
    
    def set_log_level(self, level: str) -> None:
        """Change logging level."""
        self.log_level = level
        logger.info(f"Log level changed to: {level}")
        # Note: Would need to reconfigure loguru handlers to change level dynamically
    
    def get_log_files(self) -> Dict[str, Path]:
        """Get paths to log files."""
        return {
            "app": self.log_dir / "app.log",
            "errors": self.log_dir / "errors.log"
        }


class ErrorHandler:
    """Centralized error handling."""
    
    @staticmethod
    def handle_error(
        error: Union[Exception, AppError], 
        context: Optional[Dict[str, Any]] = None,
        reraise: bool = True
    ) -> None:
        """Handle an error with proper logging and context.
        
        Args:
            error: The error to handle
            context: Additional context information
            reraise: Whether to reraise the error after handling
        """
        context = context or {}
        
        if isinstance(error, AppError):
            # Structured application error
            error_data = error.to_dict()
            error_data.update(context)
            
            logger.error(
                f"Application error: {error.message}",
                extra=error_data
            )
            
            # Log to structured logger for analysis
            struct_logger = structlog.get_logger()
            struct_logger.error(
                "application_error",
                error_code=error.code.value,
                message=error.message,
                details=error.details,
                context=context
            )
        else:
            # Generic exception
            logger.error(
                f"Unexpected error: {str(error)}",
                extra={
                    "error_type": type(error).__name__,
                    "error_message": str(error),
                    "context": context,
                    "traceback": traceback.format_exc()
                }
            )
            
            # Convert to AppError for consistency
            app_error = AppError(
                code=ErrorCode.UNKNOWN_ERROR,
                message=f"Unexpected error: {str(error)}",
                details=context,
                cause=error
            )
            
            if reraise:
                raise app_error from error
    
    @staticmethod
    def create_error(
        code: ErrorCode, 
        message: str, 
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ) -> AppError:
        """Create a structured application error."""
        return AppError(
            code=code,
            message=message,
            details=details,
            cause=cause
        )
    
    @staticmethod
    def log_performance(operation: str, duration: float, context: Optional[Dict[str, Any]] = None) -> None:
        """Log performance metrics."""
        context = context or {}
        context.update({
            "operation": operation,
            "duration_ms": round(duration * 1000, 2)
        })
        
        logger.info(f"Performance: {operation} took {duration:.3f}s", extra=context)
        
        # Structured logging for metrics
        struct_logger = structlog.get_logger()
        struct_logger.info(
            "performance_metric",
            operation=operation,
            duration_seconds=duration,
            **context
        )


# Global error handler instance
error_handler = ErrorHandler()

# Convenience functions
def log_error(error: Union[Exception, AppError], context: Optional[Dict[str, Any]] = None) -> None:
    """Log an error without reraising."""
    error_handler.handle_error(error, context, reraise=False)

def create_error(code: ErrorCode, message: str, **kwargs) -> AppError:
    """Create an application error."""
    return error_handler.create_error(code, message, **kwargs)

def log_performance(operation: str, duration: float, **context) -> None:
    """Log performance metrics."""
    error_handler.log_performance(operation, duration, context)

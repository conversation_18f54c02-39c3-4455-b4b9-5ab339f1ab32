"""
Main CLI Application Entry Point

CLI entry point with Click framework, command routing, and application lifecycle management.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import click
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from loguru import logger

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from core.config.manager import ConfigManager
from core.auth.provider_manager import ProviderManager
from core.auth.storage import SecureStorage
from core.logging import Lo<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON>, <PERSON>rrorCode, create_error
from core.base import ProviderType


console = Console()


class CLIApp:
    """Main CLI application class."""
    
    def __init__(self):
        """Initialize CLI application."""
        self.config_manager: Optional[ConfigManager] = None
        self.provider_manager: Optional[ProviderManager] = None
        self.secure_storage: Optional[SecureStorage] = None
        self.logging_manager: Optional[LoggingManager] = None
        self.error_handler = ErrorHandler()
    
    def initialize(self, log_level: str = "INFO") -> None:
        """Initialize application components."""
        try:
            # Initialize logging
            self.logging_manager = LoggingManager(log_level=log_level)
            logger.info("AI CLI Terminal System starting...")
            
            # Initialize configuration
            self.config_manager = ConfigManager()
            logger.info("Configuration manager initialized")

            # Initialize secure storage
            self.secure_storage = SecureStorage()
            logger.info("Secure storage initialized")

            # Initialize provider manager
            self.provider_manager = ProviderManager(self.config_manager, self.secure_storage)
            logger.info("Provider manager initialized")
            
            # Show welcome message
            self.show_welcome()
            
        except Exception as e:
            console.print(f"[red]Failed to initialize application: {e}[/red]")
            sys.exit(1)
    
    def show_welcome(self) -> None:
        """Show welcome message."""
        welcome_text = Text()
        welcome_text.append("🤖 AI CLI Terminal System\n", style="bold blue")
        welcome_text.append("Multi-Provider AI Assistant with Tool Support\n\n", style="cyan")
        welcome_text.append("Supported Providers: ", style="white")
        welcome_text.append("OpenAI • Anthropic • Google • Deepseek", style="green")
        
        panel = Panel(
            welcome_text,
            title="Welcome",
            border_style="blue",
            padding=(1, 2)
        )
        console.print(panel)
    
    def check_configuration(self) -> bool:
        """Check if application is properly configured."""
        if not self.config_manager:
            return False
        
        # Check if any provider is configured
        config = self.config_manager.config
        configured_providers = [
            provider_type for provider_type in ProviderType
            if self.config_manager.is_provider_configured(provider_type)
        ]
        
        if not configured_providers:
            console.print("\n[yellow]⚠️  No AI providers are configured.[/yellow]")
            console.print("Please run the setup command to configure your API keys.")
            return False
        
        return True


# Global app instance
app = CLIApp()


@click.group()
@click.option('--log-level', default='INFO', 
              type=click.Choice(['TRACE', 'DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']),
              help='Set logging level')
@click.option('--config-dir', type=click.Path(), help='Configuration directory path')
def cli(log_level: str, config_dir: Optional[str]) -> None:
    """AI CLI Terminal System - Multi-Provider AI Assistant with Tool Support."""
    try:
        app.initialize(log_level=log_level)
        if config_dir:
            app.config_manager = ConfigManager(Path(config_dir))
    except Exception as e:
        console.print(f"[red]Initialization failed: {e}[/red]")
        sys.exit(1)


@cli.command()
def setup() -> None:
    """Set up AI provider configuration and API keys."""
    console.print("\n[bold blue]🔧 AI Provider Setup[/bold blue]")
    console.print("This will guide you through configuring AI providers and API keys.\n")
    
    # Import here to avoid circular imports
    from components.auth.setup import AuthSetup
    
    try:
        auth_setup = AuthSetup(app.config_manager, app.provider_manager)
        asyncio.run(auth_setup.run_setup())
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        console.print(f"[red]Setup failed: {e}[/red]")
        sys.exit(1)


@cli.command()
def chat() -> None:
    """Start interactive chat session."""
    if not app.check_configuration():
        console.print("\n[yellow]Run 'ai-cli setup' first to configure providers.[/yellow]")
        return
    
    console.print("\n[bold green]💬 Starting chat session...[/bold green]")
    console.print("[dim]Type 'exit' or 'quit' to end the session[/dim]\n")
    
    # Import here to avoid circular imports
    from components.chat.session import ChatSession
    
    try:
        chat_session = ChatSession(app.config_manager)
        asyncio.run(chat_session.start())
    except KeyboardInterrupt:
        console.print("\n[yellow]Chat session interrupted by user[/yellow]")
    except Exception as e:
        logger.error(f"Chat session failed: {e}")
        console.print(f"[red]Chat session failed: {e}[/red]")


@cli.command()
@click.argument('message', required=False)
def ask(message: Optional[str]) -> None:
    """Ask a single question to the AI."""
    if not app.check_configuration():
        console.print("\n[yellow]Run 'ai-cli setup' first to configure providers.[/yellow]")
        return
    
    if not message:
        message = click.prompt("Enter your question")
    
    # Import here to avoid circular imports
    from components.chat.single import SingleQuery
    
    try:
        single_query = SingleQuery(app.config_manager)
        asyncio.run(single_query.process(message))
    except Exception as e:
        logger.error(f"Query failed: {e}")
        console.print(f"[red]Query failed: {e}[/red]")


@cli.command()
def test() -> None:
    """Test provider connections and validate configurations."""
    if not app.check_configuration():
        console.print("\n[yellow]No providers configured. Run 'ai-cli setup' first.[/yellow]")
        return

    console.print("\n[bold blue]🧪 Testing Provider Connections[/bold blue]")

    # Import here to avoid circular imports
    from components.auth.setup import AuthSetup

    try:
        auth_setup = AuthSetup(app.config_manager, app.provider_manager)
        asyncio.run(auth_setup.validate_all_providers())
    except Exception as e:
        logger.error(f"Provider testing failed: {e}")
        console.print(f"[red]Provider testing failed: {e}[/red]")


@cli.command()
def status() -> None:
    """Show application status and configuration."""
    console.print("\n[bold blue]📊 AI CLI Terminal Status[/bold blue]\n")
    
    if not app.config_manager:
        console.print("[red]Configuration not initialized[/red]")
        return
    
    config = app.config_manager.config
    
    # Show current provider
    console.print(f"[bold]Current Provider:[/bold] {config.current_provider.value}")
    
    # Show configured providers
    console.print("\n[bold]Configured Providers:[/bold]")
    for provider_type in ProviderType:
        is_configured = app.config_manager.is_provider_configured(provider_type)
        status_icon = "✅" if is_configured else "❌"
        status_text = "Configured" if is_configured else "Not configured"
        console.print(f"  {status_icon} {provider_type.value}: {status_text}")
    
    # Show configuration
    console.print(f"\n[bold]Configuration:[/bold]")
    console.print(f"  Auto-approve tools: {config.auto_approve_tools}")
    console.print(f"  History enabled: {config.history_enabled}")
    console.print(f"  Max history size: {config.max_history_size}")
    console.print(f"  Log level: {config.log_level}")
    
    # Show log files
    if app.logging_manager:
        log_files = app.logging_manager.get_log_files()
        console.print(f"\n[bold]Log Files:[/bold]")
        for log_type, log_path in log_files.items():
            console.print(f"  {log_type}: {log_path}")


@cli.command()
def version() -> None:
    """Show version information."""
    from .. import __version__, __description__
    
    console.print(f"\n[bold blue]AI CLI Terminal System[/bold blue]")
    console.print(f"Version: {__version__}")
    console.print(f"Description: {__description__}")


if __name__ == '__main__':
    cli()

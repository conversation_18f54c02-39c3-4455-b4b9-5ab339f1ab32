"""
Google Provider Implementation

Google provider class with Gemini models support, streaming responses, and function calling capabilities.
"""

import asyncio
from typing import AsyncGenerator, List, Dict, Any, Optional
import google.generativeai as genai
from google.generativeai.types import Harm<PERSON>ategory, HarmBlockThreshold
from loguru import logger

from ..base import BaseProvider, ProviderConfig, Message, MessageRole, ToolCall, ToolResult
from ..logging import create_error, ErrorCode


class GoogleProvider(BaseProvider):
    """Google provider implementation."""
    
    def __init__(self, config: ProviderConfig):
        """Initialize Google provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        
        # Configure Google AI
        genai.configure(api_key=config.api_key)
        
        # Model mapping for Google
        self.model_mapping = {
            "gemini-1.5-pro": "gemini-1.5-pro",
            "gemini-1.5-flash": "gemini-1.5-flash",
            "gemini-1.0-pro": "gemini-1.0-pro"
        }
        
        # Initialize model
        self.model = genai.GenerativeModel(
            model_name=self._get_model_name(),
            safety_settings={
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
        )
        
        logger.info(f"Google provider initialized with model: {config.model}")
    
    async def validate_connection(self) -> bool:
        """Validate connection to Google AI API.
        
        Returns:
            True if connection is valid
        """
        try:
            # Test with a simple completion
            response = await asyncio.to_thread(
                self.model.generate_content,
                "Hello",
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=5,
                    temperature=0.1
                )
            )
            
            if response.text:
                logger.debug("Google AI connection validated successfully")
                return True
            else:
                logger.warning("Google AI returned empty response during validation")
                return False
            
        except Exception as e:
            logger.error(f"Google AI connection validation failed: {e}")
            return False
    
    async def send_message(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """Send messages to Google AI and get streaming response.
        
        Args:
            messages: List of conversation messages
            tools: Available tools for function calling
            stream: Whether to stream the response
            
        Yields:
            Response chunks as they arrive
        """
        try:
            # Convert messages to Google format
            google_messages = self._convert_messages(messages)
            
            # Prepare generation config
            generation_config = genai.types.GenerationConfig(
                temperature=self.config.temperature or 0.7,
                max_output_tokens=self.config.max_tokens or 4096
            )
            
            # Prepare tools if provided
            google_tools = None
            if tools:
                google_tools = self._convert_tools(tools)
            
            logger.debug(f"Sending request to Google AI: {len(google_messages)} messages")
            
            if stream:
                async for chunk in self._stream_response(
                    google_messages, generation_config, google_tools
                ):
                    yield chunk
            else:
                response = await asyncio.to_thread(
                    self.model.generate_content,
                    google_messages,
                    generation_config=generation_config,
                    tools=google_tools
                )
                
                if response.text:
                    yield response.text
                
                # Handle function calls
                if response.candidates and response.candidates[0].content.parts:
                    for part in response.candidates[0].content.parts:
                        if hasattr(part, 'function_call'):
                            tool_info = f"\n[Tool Call: {part.function_call.name}]"
                            if part.function_call.args:
                                tool_info += f"\nArguments: {dict(part.function_call.args)}"
                            yield tool_info
                    
        except Exception as e:
            logger.error(f"Google AI API error: {e}")
            
            # Check for specific error types
            error_str = str(e).lower()
            if "api key" in error_str or "authentication" in error_str:
                raise create_error(
                    ErrorCode.AUTH_INVALID_API_KEY,
                    "Google AI API key is invalid or expired",
                    cause=e
                )
            elif "quota" in error_str or "rate limit" in error_str:
                raise create_error(
                    ErrorCode.PROVIDER_RATE_LIMIT,
                    "Google AI rate limit exceeded. Please try again later.",
                    cause=e
                )
            elif "timeout" in error_str:
                raise create_error(
                    ErrorCode.PROVIDER_TIMEOUT,
                    "Google AI request timed out. Please try again.",
                    cause=e
                )
            else:
                raise create_error(
                    ErrorCode.PROVIDER_API_ERROR,
                    f"Google AI API error: {e}",
                    cause=e
                )
    
    async def _stream_response(
        self,
        messages: List[Any],
        generation_config: Any,
        tools: Optional[List[Any]] = None
    ) -> AsyncGenerator[str, None]:
        """Stream response from Google AI.
        
        Args:
            messages: Google-formatted messages
            generation_config: Generation configuration
            tools: Google-formatted tools
            
        Yields:
            Response chunks
        """
        try:
            # Google AI streaming is synchronous, so we run it in a thread
            def generate_stream():
                return self.model.generate_content(
                    messages,
                    generation_config=generation_config,
                    tools=tools,
                    stream=True
                )
            
            stream = await asyncio.to_thread(generate_stream)
            
            for chunk in stream:
                if chunk.text:
                    yield chunk.text
                
                # Handle function calls in streaming
                if chunk.candidates and chunk.candidates[0].content.parts:
                    for part in chunk.candidates[0].content.parts:
                        if hasattr(part, 'function_call'):
                            tool_info = f"\n[Tool Call: {part.function_call.name}]"
                            if part.function_call.args:
                                tool_info += f"\nArguments: {dict(part.function_call.args)}"
                            yield tool_info
                            
        except Exception as e:
            logger.error(f"Error streaming Google AI response: {e}")
            raise
    
    def _convert_messages(self, messages: List[Message]) -> List[Any]:
        """Convert internal messages to Google format.
        
        Args:
            messages: Internal message format
            
        Returns:
            Google-formatted messages
        """
        google_messages = []
        
        for message in messages:
            # Convert role
            role = self._convert_role(message.role)
            
            # Create parts list
            parts = []
            
            # Add text content
            if message.content:
                parts.append(message.content)
            
            # Handle tool calls
            if message.tool_calls:
                for tool_call in message.tool_calls:
                    # Google AI uses function_call format
                    function_call = genai.types.FunctionCall(
                        name=tool_call.name,
                        args=tool_call.arguments
                    )
                    parts.append(function_call)
            
            # Handle tool results
            if message.tool_results:
                for tool_result in message.tool_results:
                    # Google AI uses function_response format
                    function_response = genai.types.FunctionResponse(
                        name=tool_result.tool_call_id,  # Use tool_call_id as name
                        response={"result": tool_result.content}
                    )
                    parts.append(function_response)
            
            google_message = genai.types.ContentDict(
                role=role,
                parts=parts
            )
            
            google_messages.append(google_message)
        
        return google_messages
    
    def _convert_role(self, role: MessageRole) -> str:
        """Convert internal role to Google role.
        
        Args:
            role: Internal message role
            
        Returns:
            Google role string
        """
        role_mapping = {
            MessageRole.USER: "user",
            MessageRole.ASSISTANT: "model",
            MessageRole.SYSTEM: "user",  # Google treats system as user
            MessageRole.TOOL: "function"
        }
        return role_mapping.get(role, "user")
    
    def _convert_tools(self, tools: List[Dict[str, Any]]) -> List[Any]:
        """Convert tools to Google function format.
        
        Args:
            tools: Tool definitions
            
        Returns:
            Google-formatted tools
        """
        google_tools = []
        
        for tool in tools:
            function_declaration = genai.types.FunctionDeclaration(
                name=tool.get("name", ""),
                description=tool.get("description", ""),
                parameters=tool.get("parameters", {})
            )
            
            google_tool = genai.types.Tool(
                function_declarations=[function_declaration]
            )
            
            google_tools.append(google_tool)
        
        return google_tools
    
    def _get_model_name(self) -> str:
        """Get the actual model name for Google AI API.
        
        Returns:
            Google model name
        """
        return self.model_mapping.get(self.config.model, self.config.model)
    
    async def get_available_models(self) -> List[str]:
        """Get list of available models.
        
        Returns:
            List of available model names
        """
        try:
            models = await asyncio.to_thread(genai.list_models)
            # Filter for generative models
            generative_models = [
                model.name.replace("models/", "") for model in models
                if "generateContent" in model.supported_generation_methods
            ]
            return sorted(generative_models)
        except Exception as e:
            logger.warning(f"Could not fetch Google AI models: {e}")
            # Return default models
            return list(self.model_mapping.keys())
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information.
        
        Returns:
            Provider information dictionary
        """
        return {
            "name": "Google",
            "type": "google",
            "model": self.config.model,
            "supports_streaming": True,
            "supports_function_calling": True,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "timeout": self.config.timeout
        }

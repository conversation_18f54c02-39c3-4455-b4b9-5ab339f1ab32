"""
Base Classes and Interfaces

Abstract base classes for providers, tools, and core components with proper type hints.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, AsyncGenerator, Callable
from enum import Enum
from dataclasses import dataclass
from pydantic import BaseModel, Field
import asyncio


class ProviderType(Enum):
    """Supported AI provider types."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    DEEPSEEK = "deepseek"


class MessageRole(Enum):
    """Message roles in conversation."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


@dataclass
class Message:
    """Represents a message in the conversation."""
    role: MessageRole
    content: str
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ToolCall:
    """Represents a tool call request from the AI."""
    id: str
    name: str
    arguments: Dict[str, Any]


@dataclass
class ToolResult:
    """Represents the result of a tool execution."""
    success: bool
    content: str
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class StreamChunk:
    """Represents a chunk of streaming response."""
    def __init__(self, content: str, is_complete: bool = False, metadata: Optional[Dict[str, Any]] = None):
        self.content = content
        self.is_complete = is_complete
        self.metadata = metadata or {}


class BaseProvider(ABC):
    """Abstract base class for AI providers."""
    
    def __init__(self, api_key: str, model: str, **kwargs):
        self.api_key = api_key
        self.model = model
        self.config = kwargs
    
    @abstractmethod
    async def send_message(
        self, 
        messages: List[Message], 
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = False
    ) -> Union[Message, AsyncGenerator[StreamChunk, None]]:
        """Send a message and get response."""
        pass
    
    @abstractmethod
    async def validate_connection(self) -> bool:
        """Validate API connection and credentials."""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[str]:
        """Get list of available models for this provider."""
        pass
    
    @property
    @abstractmethod
    def provider_type(self) -> ProviderType:
        """Get the provider type."""
        pass


class BaseTool(ABC):
    """Abstract base class for tools."""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    @abstractmethod
    def get_schema(self) -> Dict[str, Any]:
        """Get the JSON schema for this tool."""
        pass
    
    @abstractmethod
    async def validate_params(self, params: Dict[str, Any]) -> bool:
        """Validate tool parameters."""
        pass
    
    @abstractmethod
    async def should_confirm_execute(self, params: Dict[str, Any]) -> bool:
        """Check if user confirmation is needed before execution."""
        pass
    
    @abstractmethod
    async def execute(self, params: Dict[str, Any]) -> ToolResult:
        """Execute the tool with given parameters."""
        pass


class ToolRegistry:
    """Registry for managing available tools."""
    
    def __init__(self):
        self._tools: Dict[str, BaseTool] = {}
    
    def register(self, tool: BaseTool) -> None:
        """Register a tool."""
        self._tools[tool.name] = tool
    
    def unregister(self, name: str) -> None:
        """Unregister a tool."""
        if name in self._tools:
            del self._tools[name]
    
    def get_tool(self, name: str) -> Optional[BaseTool]:
        """Get a tool by name."""
        return self._tools.get(name)
    
    def get_all_tools(self) -> List[BaseTool]:
        """Get all registered tools."""
        return list(self._tools.values())
    
    def get_schemas(self) -> List[Dict[str, Any]]:
        """Get schemas for all registered tools."""
        return [tool.get_schema() for tool in self._tools.values()]


class BaseConfig(BaseModel):
    """Base configuration class."""
    
    class Config:
        """Pydantic configuration."""
        extra = "forbid"
        validate_assignment = True


class ProviderConfig(BaseConfig):
    """Configuration for AI providers."""
    provider_type: ProviderType
    api_key: str = Field(..., description="API key for the provider")
    model: str = Field(..., description="Model name to use")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens in response")
    temperature: Optional[float] = Field(None, description="Temperature for response generation")
    timeout: Optional[int] = Field(30, description="Request timeout in seconds")


class AppConfig(BaseConfig):
    """Main application configuration."""
    current_provider: ProviderType = Field(ProviderType.OPENAI, description="Currently selected provider")
    providers: Dict[ProviderType, ProviderConfig] = Field(default_factory=dict, description="Provider configurations")
    auto_approve_tools: bool = Field(False, description="Auto-approve safe tool executions")
    history_enabled: bool = Field(True, description="Enable conversation history")
    max_history_size: int = Field(1000, description="Maximum number of messages in history")
    log_level: str = Field("INFO", description="Logging level")


class BaseEngine(ABC):
    """Abstract base class for the core engine."""
    
    def __init__(self, config: AppConfig, tool_registry: ToolRegistry):
        self.config = config
        self.tool_registry = tool_registry
    
    @abstractmethod
    async def process_request(
        self, 
        user_input: str, 
        conversation_history: List[Message]
    ) -> AsyncGenerator[StreamChunk, None]:
        """Process a user request and return streaming response."""
        pass
    
    @abstractmethod
    async def handle_tool_call(self, tool_call: ToolCall) -> ToolResult:
        """Handle a tool call request."""
        pass

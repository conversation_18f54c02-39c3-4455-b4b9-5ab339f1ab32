"""
Provider Factory

Factory pattern implementation for creating AI provider instances based on configuration.
"""

from typing import Dict, Type, Optional
from loguru import logger

from ..base import BaseProvider, ProviderType, ProviderConfig
from ..logging import create_error, ErrorCode


class ProviderFactory:
    """Factory for creating AI provider instances."""
    
    # Registry of provider classes
    _providers: Dict[ProviderType, Type[BaseProvider]] = {}
    
    @classmethod
    def register_provider(cls, provider_type: ProviderType, provider_class: Type[BaseProvider]) -> None:
        """Register a provider class.
        
        Args:
            provider_type: The provider type
            provider_class: The provider class to register
        """
        cls._providers[provider_type] = provider_class
        logger.debug(f"Registered provider: {provider_type.value} -> {provider_class.__name__}")
    
    @classmethod
    def create_provider(cls, config: ProviderConfig) -> BaseProvider:
        """Create a provider instance from configuration.
        
        Args:
            config: Provider configuration
            
        Returns:
            Provider instance
            
        Raises:
            AppError: If provider type is not supported or creation fails
        """
        provider_type = config.provider_type
        
        if provider_type not in cls._providers:
            raise create_error(
                ErrorCode.PROVIDER_NOT_CONFIGURED,
                f"Provider type {provider_type.value} is not registered"
            )
        
        provider_class = cls._providers[provider_type]
        
        try:
            provider = provider_class(config)
            logger.debug(f"Created provider instance: {provider_type.value}")
            return provider
        except Exception as e:
            logger.error(f"Failed to create provider {provider_type.value}: {e}")
            raise create_error(
                ErrorCode.PROVIDER_API_ERROR,
                f"Failed to create provider {provider_type.value}: {e}",
                cause=e
            )
    
    @classmethod
    def get_supported_providers(cls) -> list[ProviderType]:
        """Get list of supported provider types.
        
        Returns:
            List of supported provider types
        """
        return list(cls._providers.keys())
    
    @classmethod
    def is_provider_supported(cls, provider_type: ProviderType) -> bool:
        """Check if a provider type is supported.
        
        Args:
            provider_type: The provider type to check
            
        Returns:
            True if provider is supported
        """
        return provider_type in cls._providers
    
    @classmethod
    def get_provider_class(cls, provider_type: ProviderType) -> Optional[Type[BaseProvider]]:
        """Get the provider class for a provider type.
        
        Args:
            provider_type: The provider type
            
        Returns:
            Provider class if registered, None otherwise
        """
        return cls._providers.get(provider_type)


# Auto-register providers when they are imported
def _auto_register_providers():
    """Auto-register all available providers."""
    try:
        from .openai_provider import OpenAIProvider
        ProviderFactory.register_provider(ProviderType.OPENAI, OpenAIProvider)
    except ImportError as e:
        logger.warning(f"OpenAI provider not available: {e}")
    
    try:
        from .anthropic_provider import AnthropicProvider
        ProviderFactory.register_provider(ProviderType.ANTHROPIC, AnthropicProvider)
    except ImportError as e:
        logger.warning(f"Anthropic provider not available: {e}")
    
    try:
        from .google_provider import GoogleProvider
        ProviderFactory.register_provider(ProviderType.GOOGLE, GoogleProvider)
    except ImportError as e:
        logger.warning(f"Google provider not available: {e}")
    
    try:
        from .deepseek_provider import DeepseekProvider
        ProviderFactory.register_provider(ProviderType.DEEPSEEK, DeepseekProvider)
    except ImportError as e:
        logger.warning(f"Deepseek provider not available: {e}")


# Register providers on module import
_auto_register_providers()

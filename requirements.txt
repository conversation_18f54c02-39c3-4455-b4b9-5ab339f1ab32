# AI CLI Terminal System - Dependencies (Latest 2024 versions)

# Core CLI Framework
rich==13.7.1                    # Beautiful CLI interface and formatting
click==8.1.7                    # Command-line interface framework
prompt-toolkit==3.0.43          # Advanced input handling and auto-completion

# Data Validation and Models
pydantic==2.5.3                 # Data validation and settings management
pydantic-settings==2.1.0        # Settings management with Pydantic

# AI Provider SDKs
openai==1.12.0                  # OpenAI API client
anthropic==0.18.1               # Anthropic Claude API client
google-generativeai==0.4.0      # Google Gemini API client

# HTTP and Async Operations
requests==2.31.0                # HTTP requests
aiohttp==3.9.3                  # Async HTTP client
httpx==0.26.0                   # Modern HTTP client with async support

# Security and Encryption
cryptography==42.0.2            # Encryption for API key storage
keyring==24.3.0                 # Secure credential storage

# Configuration and Environment
python-dotenv==1.0.1            # Environment variable management
toml==0.10.2                    # TOML configuration file support
pyyaml==6.0.1                   # YAML configuration file support

# File and System Operations
pathlib2==2.3.7                 # Enhanced path operations
watchdog==4.0.0                 # File system monitoring
psutil==5.9.8                   # System and process utilities

# Text Processing and Search
regex==2023.12.25               # Advanced regex operations
fuzzywuzzy==0.18.0              # Fuzzy string matching
python-levenshtein==0.25.0      # String distance calculations

# Logging and Debugging
loguru==0.7.2                   # Advanced logging
structlog==23.2.0               # Structured logging

# Development and Testing
pytest==8.0.0                   # Testing framework
pytest-asyncio==0.23.4          # Async testing support
black==24.1.1                   # Code formatting
flake8==7.0.0                   # Code linting
mypy==1.8.0                     # Type checking

# Optional: Web scraping and content processing (for web tools)
beautifulsoup4==4.12.3          # HTML parsing
lxml==5.1.0                     # XML/HTML processing
markdownify==0.11.6             # HTML to Markdown conversion

# Optional: Additional utilities
colorama==0.4.6                 # Cross-platform colored terminal text
tqdm==4.66.1                    # Progress bars
tabulate==0.9.0                 # Table formatting

"""
Authentication Setup Component

Interactive authentication interface that shows on startup for configuring provider,
API key, and model selection.
"""

import asyncio
from typing import Dict, List, Optional, Tuple
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn
from prompt_toolkit import prompt
from prompt_toolkit.completion import WordCompleter
from loguru import logger

from ...core.base import ProviderType, ProviderConfig
from ...core.config.manager import ConfigManager
from ...core.auth.provider_manager import ProviderManager
from ...core.logging import create_error, ErrorCode


console = Console()


class AuthSetup:
    """Interactive authentication setup wizard."""
    
    # Available models for each provider
    PROVIDER_MODELS = {
        ProviderType.OPENAI: [
            "gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo"
        ],
        ProviderType.ANTHROPIC: [
            "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", 
            "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"
        ],
        ProviderType.GOOGLE: [
            "gemini-1.5-pro", "gemini-1.5-flash", "gemini-1.0-pro"
        ],
        ProviderType.DEEPSEEK: [
            "deepseek-chat", "deepseek-reasoner"
        ]
    }
    
    def __init__(self, config_manager: ConfigManager, provider_manager: ProviderManager):
        """Initialize authentication setup.

        Args:
            config_manager: Configuration manager instance
            provider_manager: Provider manager instance
        """
        self.config_manager = config_manager
        self.provider_manager = provider_manager
    
    async def run_setup(self) -> None:
        """Run the complete authentication setup process."""
        try:
            console.print("\n[bold blue]🔐 Authentication Setup Wizard[/bold blue]")
            console.print("Configure your AI providers to get started.\n")
            
            # Show current configuration status
            self._show_current_status()
            
            # Main setup loop
            while True:
                action = self._get_main_action()
                
                if action == "add":
                    await self._add_provider()
                elif action == "edit":
                    await self._edit_provider()
                elif action == "remove":
                    await self._remove_provider()
                elif action == "switch":
                    await self._switch_provider()
                elif action == "test":
                    await self._test_provider()
                elif action == "done":
                    break
            
            # Final status
            console.print("\n[green]✅ Setup completed![/green]")
            self._show_current_status()
            
        except KeyboardInterrupt:
            console.print("\n[yellow]Setup cancelled by user[/yellow]")
        except Exception as e:
            logger.error(f"Setup failed: {e}")
            console.print(f"\n[red]Setup failed: {e}[/red]")
    
    def _show_current_status(self) -> None:
        """Show current provider configuration status."""
        config = self.config_manager.config
        
        table = Table(title="Provider Configuration Status", show_header=True, header_style="bold blue")
        table.add_column("Provider", style="cyan")
        table.add_column("Status", style="white")
        table.add_column("Model", style="green")
        table.add_column("Current", style="yellow")
        
        for provider_type in ProviderType:
            is_configured = self.config_manager.is_provider_configured(provider_type)
            status = "✅ Configured" if is_configured else "❌ Not configured"
            
            model = ""
            is_current = ""
            if is_configured:
                provider_config = config.providers[provider_type]
                model = provider_config.model
                is_current = "🔸 Active" if provider_type == config.current_provider else ""
            
            table.add_row(
                provider_type.value.title(),
                status,
                model,
                is_current
            )
        
        console.print(table)
        console.print()
    
    def _get_main_action(self) -> str:
        """Get the main action from user."""
        actions = {
            "1": ("add", "Add/Configure a provider"),
            "2": ("edit", "Edit existing provider"),
            "3": ("remove", "Remove a provider"),
            "4": ("switch", "Switch current provider"),
            "5": ("test", "Test provider connection"),
            "6": ("done", "Finish setup")
        }
        
        console.print("[bold]What would you like to do?[/bold]")
        for key, (_, description) in actions.items():
            console.print(f"  {key}. {description}")
        
        choice = Prompt.ask("\nEnter your choice", choices=list(actions.keys()))
        return actions[choice][0]
    
    async def _add_provider(self) -> None:
        """Add or configure a provider."""
        console.print("\n[bold blue]➕ Add/Configure Provider[/bold blue]")
        
        # Select provider
        provider_type = self._select_provider()
        if not provider_type:
            return
        
        console.print(f"\nConfiguring [cyan]{provider_type.value.title()}[/cyan]...")
        
        # Get API key
        api_key = self._get_api_key(provider_type)
        if not api_key:
            console.print("[yellow]Configuration cancelled[/yellow]")
            return
        
        # Select model
        model = self._select_model(provider_type)
        if not model:
            console.print("[yellow]Configuration cancelled[/yellow]")
            return
        
        # Get additional configuration
        max_tokens = self._get_optional_int("Max tokens (optional)", default=None)
        temperature = self._get_optional_float("Temperature (0.0-2.0, optional)", default=None, min_val=0.0, max_val=2.0)
        
        # Create provider configuration
        provider_config = ProviderConfig(
            provider_type=provider_type,
            api_key=api_key,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        # Test connection
        console.print("\n[yellow]Testing connection...[/yellow]")
        if await self._test_provider_config(provider_config):
            # Save configuration
            self.config_manager.update_provider_config(provider_type, provider_config)
            
            # Ask if this should be the current provider
            if not self.config_manager.get_current_provider_config() or \
               Confirm.ask(f"Set {provider_type.value.title()} as the current provider?"):
                self.config_manager.set_current_provider(provider_type)
            
            console.print(f"[green]✅ {provider_type.value.title()} configured successfully![/green]")
        else:
            console.print(f"[red]❌ Failed to configure {provider_type.value.title()}[/red]")
    
    async def _edit_provider(self) -> None:
        """Edit an existing provider configuration."""
        console.print("\n[bold blue]✏️  Edit Provider[/bold blue]")
        
        # Get configured providers
        configured_providers = [
            provider_type for provider_type in ProviderType
            if self.config_manager.is_provider_configured(provider_type)
        ]
        
        if not configured_providers:
            console.print("[yellow]No providers are configured yet.[/yellow]")
            return
        
        # Select provider to edit
        provider_type = self._select_provider(configured_providers)
        if not provider_type:
            return
        
        current_config = self.config_manager.config.providers[provider_type]
        console.print(f"\nEditing [cyan]{provider_type.value.title()}[/cyan]")
        console.print(f"Current model: {current_config.model}")
        
        # Edit configuration
        api_key = self._get_api_key(provider_type, current_config.api_key)
        model = self._select_model(provider_type, current_config.model)
        max_tokens = self._get_optional_int("Max tokens", current_config.max_tokens)
        temperature = self._get_optional_float("Temperature", current_config.temperature, 0.0, 2.0)
        
        # Create updated configuration
        updated_config = ProviderConfig(
            provider_type=provider_type,
            api_key=api_key,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature
        )
        
        # Test and save
        console.print("\n[yellow]Testing updated configuration...[/yellow]")
        if await self._test_provider_config(updated_config):
            self.config_manager.update_provider_config(provider_type, updated_config)
            console.print(f"[green]✅ {provider_type.value.title()} updated successfully![/green]")
        else:
            console.print(f"[red]❌ Failed to update {provider_type.value.title()}[/red]")
    
    async def _remove_provider(self) -> None:
        """Remove a provider configuration."""
        console.print("\n[bold blue]🗑️  Remove Provider[/bold blue]")
        
        configured_providers = [
            provider_type for provider_type in ProviderType
            if self.config_manager.is_provider_configured(provider_type)
        ]
        
        if not configured_providers:
            console.print("[yellow]No providers are configured.[/yellow]")
            return
        
        provider_type = self._select_provider(configured_providers)
        if not provider_type:
            return
        
        if Confirm.ask(f"Remove {provider_type.value.title()} configuration?"):
            self.config_manager.remove_provider_config(provider_type)
            console.print(f"[green]✅ {provider_type.value.title()} removed[/green]")
    
    async def _switch_provider(self) -> None:
        """Switch the current active provider."""
        console.print("\n[bold blue]🔄 Switch Provider[/bold blue]")
        
        configured_providers = [
            provider_type for provider_type in ProviderType
            if self.config_manager.is_provider_configured(provider_type)
        ]
        
        if len(configured_providers) < 2:
            console.print("[yellow]Need at least 2 configured providers to switch.[/yellow]")
            return
        
        current = self.config_manager.config.current_provider
        console.print(f"Current provider: [cyan]{current.value.title()}[/cyan]")
        
        provider_type = self._select_provider(configured_providers)
        if provider_type and provider_type != current:
            self.config_manager.set_current_provider(provider_type)
            console.print(f"[green]✅ Switched to {provider_type.value.title()}[/green]")
    
    async def _test_provider(self) -> None:
        """Test a provider connection."""
        console.print("\n[bold blue]🧪 Test Provider[/bold blue]")
        
        configured_providers = [
            provider_type for provider_type in ProviderType
            if self.config_manager.is_provider_configured(provider_type)
        ]
        
        if not configured_providers:
            console.print("[yellow]No providers are configured.[/yellow]")
            return
        
        provider_type = self._select_provider(configured_providers)
        if not provider_type:
            return
        
        config = self.config_manager.config.providers[provider_type]
        console.print(f"\nTesting [cyan]{provider_type.value.title()}[/cyan]...")
        
        success = await self._test_provider_config(config)
        if success:
            console.print(f"[green]✅ {provider_type.value.title()} connection successful![/green]")
        else:
            console.print(f"[red]❌ {provider_type.value.title()} connection failed[/red]")
    
    def _select_provider(self, available_providers: Optional[List[ProviderType]] = None) -> Optional[ProviderType]:
        """Select a provider from available options."""
        providers = available_providers or list(ProviderType)
        
        if len(providers) == 1:
            return providers[0]
        
        console.print("\n[bold]Select a provider:[/bold]")
        for i, provider in enumerate(providers, 1):
            console.print(f"  {i}. {provider.value.title()}")
        
        try:
            choice = int(Prompt.ask("Enter choice", default="1")) - 1
            if 0 <= choice < len(providers):
                return providers[choice]
        except (ValueError, IndexError):
            console.print("[red]Invalid choice[/red]")
        
        return None
    
    def _get_api_key(self, provider_type: ProviderType, current_key: Optional[str] = None) -> Optional[str]:
        """Get API key from user."""
        provider_name = provider_type.value.title()
        
        if current_key:
            masked_key = f"{current_key[:8]}...{current_key[-4:]}" if len(current_key) > 12 else "***"
            console.print(f"Current API key: {masked_key}")
            if not Confirm.ask("Update API key?"):
                return current_key
        
        console.print(f"\n[bold]Enter your {provider_name} API key:[/bold]")
        console.print(f"[dim]Get your API key from the {provider_name} dashboard[/dim]")
        
        api_key = prompt("API Key: ", is_password=True)
        return api_key.strip() if api_key else None
    
    def _select_model(self, provider_type: ProviderType, current_model: Optional[str] = None) -> Optional[str]:
        """Select a model for the provider."""
        models = self.PROVIDER_MODELS.get(provider_type, [])
        
        if not models:
            return Prompt.ask("Enter model name")
        
        console.print(f"\n[bold]Select a model for {provider_type.value.title()}:[/bold]")
        for i, model in enumerate(models, 1):
            current_indicator = " (current)" if model == current_model else ""
            console.print(f"  {i}. {model}{current_indicator}")
        
        try:
            choice = int(Prompt.ask("Enter choice", default="1")) - 1
            if 0 <= choice < len(models):
                return models[choice]
        except (ValueError, IndexError):
            console.print("[red]Invalid choice[/red]")
        
        return None
    
    def _get_optional_int(self, prompt_text: str, default: Optional[int] = None) -> Optional[int]:
        """Get optional integer input."""
        default_str = str(default) if default is not None else ""
        value = Prompt.ask(prompt_text, default=default_str)
        
        if not value:
            return None
        
        try:
            return int(value)
        except ValueError:
            console.print("[red]Invalid number[/red]")
            return default
    
    def _get_optional_float(self, prompt_text: str, default: Optional[float] = None, 
                           min_val: Optional[float] = None, max_val: Optional[float] = None) -> Optional[float]:
        """Get optional float input with validation."""
        default_str = str(default) if default is not None else ""
        value = Prompt.ask(prompt_text, default=default_str)
        
        if not value:
            return None
        
        try:
            float_val = float(value)
            if min_val is not None and float_val < min_val:
                console.print(f"[red]Value must be >= {min_val}[/red]")
                return default
            if max_val is not None and float_val > max_val:
                console.print(f"[red]Value must be <= {max_val}[/red]")
                return default
            return float_val
        except ValueError:
            console.print("[red]Invalid number[/red]")
            return default
    
    async def _test_provider_config(self, config: ProviderConfig) -> bool:
        """Test a provider configuration."""
        try:
            # Import provider factory here to avoid circular imports
            from ...core.providers.factory import ProviderFactory
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Testing connection...", total=None)
                
                # Create provider and test connection
                provider = ProviderFactory.create_provider(config)
                success = await provider.validate_connection()
                
                progress.update(task, completed=True)
                return success
                
        except Exception as e:
            logger.error(f"Provider test failed: {e}")
            console.print(f"[red]Connection test failed: {e}[/red]")
            return False

    async def validate_api_key(self, provider_type: ProviderType, api_key: str, model: str) -> Tuple[bool, str]:
        """Validate API key with provider.

        Args:
            provider_type: Provider type
            api_key: API key to validate
            model: Model to test with

        Returns:
            Tuple of (success, error_message)
        """
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task(f"Validating {provider_type.value} API key...", total=None)

                # Test with provider manager
                success = await self.provider_manager.test_provider_with_credentials(
                    provider_type, api_key, model
                )

                progress.update(task, completed=True)

                if success:
                    return True, ""
                else:
                    return False, "API key validation failed. Please check your credentials."

        except Exception as e:
            error_msg = str(e)
            logger.error(f"API key validation error: {error_msg}")

            # Provide user-friendly error messages
            if "authentication" in error_msg.lower() or "unauthorized" in error_msg.lower():
                return False, "Invalid API key. Please check your credentials."
            elif "rate limit" in error_msg.lower():
                return False, "Rate limit exceeded. API key is valid but temporarily limited."
            elif "timeout" in error_msg.lower():
                return False, "Connection timeout. Please check your internet connection."
            else:
                return False, f"Validation failed: {error_msg}"

    async def test_current_provider(self) -> bool:
        """Test the current provider configuration.

        Returns:
            True if current provider is working
        """
        try:
            current_provider = self.provider_manager.get_current_provider()
            if not current_provider:
                console.print("[red]No provider is currently configured.[/red]")
                return False

            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task(f"Testing {current_provider.value} connection...", total=None)

                success = await self.provider_manager.validate_current_provider()

                progress.update(task, completed=True)

                if success:
                    console.print(f"[green]✓[/green] {current_provider.value} connection is working!")
                    return True
                else:
                    console.print(f"[red]✗[/red] {current_provider.value} connection failed!")
                    return False

        except Exception as e:
            logger.error(f"Provider test failed: {e}")
            console.print(f"[red]✗[/red] Provider test failed: {e}")
            return False

    async def validate_all_providers(self) -> Dict[ProviderType, bool]:
        """Validate all configured providers.

        Returns:
            Dictionary mapping provider types to validation results
        """
        results = {}
        configured_providers = self.provider_manager.get_configured_providers()

        if not configured_providers:
            console.print("[yellow]No providers are configured.[/yellow]")
            return results

        console.print(f"\n[bold]Testing {len(configured_providers)} configured providers...[/bold]")

        for provider_type in configured_providers:
            try:
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=console
                ) as progress:
                    task = progress.add_task(f"Testing {provider_type.value}...", total=None)

                    success = await self.provider_manager.validate_provider_connection(provider_type)
                    results[provider_type] = success

                    progress.update(task, completed=True)

                    if success:
                        console.print(f"[green]✓[/green] {provider_type.value} - Working")
                    else:
                        console.print(f"[red]✗[/red] {provider_type.value} - Failed")

            except Exception as e:
                logger.error(f"Error testing {provider_type.value}: {e}")
                results[provider_type] = False
                console.print(f"[red]✗[/red] {provider_type.value} - Error: {e}")

        return results

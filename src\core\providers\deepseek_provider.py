"""
Deepseek Provider Implementation

Deepseek provider class with API integration, streaming responses, and function calling capabilities.
"""

import asyncio
import json
from typing import AsyncGenerator, List, Dict, Any, Optional
import httpx
from loguru import logger

from ..base import BaseProvider, ProviderConfig, Message, MessageRole, ToolCall, ToolResult
from ..logging import create_error, ErrorCode


class DeepseekProvider(BaseProvider):
    """Deepseek provider implementation."""
    
    def __init__(self, config: ProviderConfig):
        """Initialize Deepseek provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        
        # Deepseek API configuration
        self.base_url = "https://api.deepseek.com/v1"
        self.api_key = config.api_key
        
        # Model mapping for Deepseek
        self.model_mapping = {
            "deepseek-chat": "deepseek-chat",
            "deepseek-coder": "deepseek-coder"
        }
        
        # HTTP client configuration
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            },
            timeout=config.timeout or 30.0
        )
        
        logger.info(f"Deepseek provider initialized with model: {config.model}")
    
    async def validate_connection(self) -> bool:
        """Validate connection to Deepseek API.
        
        Returns:
            True if connection is valid
        """
        try:
            # Test with a simple completion
            response = await self.client.post(
                "/chat/completions",
                json={
                    "model": self._get_model_name(),
                    "messages": [{"role": "user", "content": "Hello"}],
                    "max_tokens": 5,
                    "stream": False
                },
                timeout=10.0
            )
            
            if response.status_code == 200:
                logger.debug("Deepseek connection validated successfully")
                return True
            elif response.status_code == 401:
                logger.error("Deepseek authentication failed - invalid API key")
                return False
            elif response.status_code == 429:
                logger.warning("Deepseek rate limit reached during validation")
                return True  # API key is valid, just rate limited
            else:
                logger.error(f"Deepseek validation failed with status: {response.status_code}")
                return False
            
        except Exception as e:
            logger.error(f"Deepseek connection validation failed: {e}")
            return False
    
    async def send_message(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """Send messages to Deepseek and get streaming response.
        
        Args:
            messages: List of conversation messages
            tools: Available tools for function calling
            stream: Whether to stream the response
            
        Yields:
            Response chunks as they arrive
        """
        try:
            # Convert messages to Deepseek format (OpenAI-compatible)
            deepseek_messages = self._convert_messages(messages)
            
            # Prepare request payload
            payload = {
                "model": self._get_model_name(),
                "messages": deepseek_messages,
                "stream": stream
            }
            
            # Add optional parameters
            if self.config.max_tokens:
                payload["max_tokens"] = self.config.max_tokens
            
            if self.config.temperature is not None:
                payload["temperature"] = self.config.temperature
            
            # Add tools if provided (Deepseek uses OpenAI-compatible format)
            if tools:
                payload["tools"] = self._convert_tools(tools)
                payload["tool_choice"] = "auto"
            
            logger.debug(f"Sending request to Deepseek: {len(deepseek_messages)} messages")
            
            if stream:
                async for chunk in self._stream_response(payload):
                    yield chunk
            else:
                response = await self.client.post("/chat/completions", json=payload)
                await self._handle_response_errors(response)
                
                data = response.json()
                if data.get("choices") and data["choices"][0].get("message", {}).get("content"):
                    yield data["choices"][0]["message"]["content"]
                    
        except httpx.HTTPStatusError as e:
            await self._handle_http_error(e)
        except Exception as e:
            logger.error(f"Deepseek API error: {e}")
            raise create_error(
                ErrorCode.PROVIDER_API_ERROR,
                f"Deepseek API error: {e}",
                cause=e
            )
    
    async def _stream_response(self, payload: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """Stream response from Deepseek.
        
        Args:
            payload: Request payload
            
        Yields:
            Response chunks
        """
        try:
            async with self.client.stream("POST", "/chat/completions", json=payload) as response:
                await self._handle_response_errors(response)
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # Remove "data: " prefix
                        
                        if data_str.strip() == "[DONE]":
                            break
                        
                        try:
                            data = json.loads(data_str)
                            
                            if data.get("choices"):
                                choice = data["choices"][0]
                                delta = choice.get("delta", {})
                                
                                # Handle content chunks
                                if "content" in delta and delta["content"]:
                                    yield delta["content"]
                                
                                # Handle tool calls
                                if "tool_calls" in delta and delta["tool_calls"]:
                                    for tool_call in delta["tool_calls"]:
                                        if tool_call.get("function"):
                                            function = tool_call["function"]
                                            tool_info = f"\n[Tool Call: {function.get('name', '')}]"
                                            if function.get("arguments"):
                                                tool_info += f"\nArguments: {function['arguments']}"
                                            yield tool_info
                                
                                # Handle finish reason
                                if choice.get("finish_reason"):
                                    logger.debug(f"Deepseek stream finished: {choice['finish_reason']}")
                        
                        except json.JSONDecodeError:
                            # Skip malformed JSON lines
                            continue
                            
        except Exception as e:
            logger.error(f"Error streaming Deepseek response: {e}")
            raise
    
    async def _handle_response_errors(self, response: httpx.Response) -> None:
        """Handle HTTP response errors.
        
        Args:
            response: HTTP response
            
        Raises:
            AppError: For various error conditions
        """
        if response.status_code == 401:
            raise create_error(
                ErrorCode.AUTH_INVALID_API_KEY,
                "Deepseek API key is invalid or expired"
            )
        elif response.status_code == 429:
            raise create_error(
                ErrorCode.PROVIDER_RATE_LIMIT,
                "Deepseek rate limit exceeded. Please try again later."
            )
        elif response.status_code >= 500:
            raise create_error(
                ErrorCode.PROVIDER_API_ERROR,
                f"Deepseek server error: {response.status_code}"
            )
        elif response.status_code >= 400:
            try:
                error_data = response.json()
                error_message = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")
            except:
                error_message = f"HTTP {response.status_code}"
            
            raise create_error(
                ErrorCode.PROVIDER_API_ERROR,
                f"Deepseek API error: {error_message}"
            )
    
    async def _handle_http_error(self, error: httpx.HTTPStatusError) -> None:
        """Handle HTTP status errors.
        
        Args:
            error: HTTP status error
        """
        response = error.response
        
        if response.status_code == 401:
            raise create_error(
                ErrorCode.AUTH_INVALID_API_KEY,
                "Deepseek API key is invalid or expired",
                cause=error
            )
        elif response.status_code == 429:
            raise create_error(
                ErrorCode.PROVIDER_RATE_LIMIT,
                "Deepseek rate limit exceeded. Please try again later.",
                cause=error
            )
        elif response.status_code >= 500:
            raise create_error(
                ErrorCode.PROVIDER_API_ERROR,
                f"Deepseek server error: {response.status_code}",
                cause=error
            )
        else:
            raise create_error(
                ErrorCode.PROVIDER_API_ERROR,
                f"Deepseek API error: HTTP {response.status_code}",
                cause=error
            )
    
    def _convert_messages(self, messages: List[Message]) -> List[Dict[str, Any]]:
        """Convert internal messages to Deepseek format (OpenAI-compatible).
        
        Args:
            messages: Internal message format
            
        Returns:
            Deepseek-formatted messages
        """
        deepseek_messages = []
        
        for message in messages:
            deepseek_message = {
                "role": self._convert_role(message.role),
                "content": message.content
            }
            
            # Add tool calls if present
            if message.tool_calls:
                deepseek_message["tool_calls"] = [
                    {
                        "id": tool_call.id,
                        "type": "function",
                        "function": {
                            "name": tool_call.name,
                            "arguments": tool_call.arguments
                        }
                    }
                    for tool_call in message.tool_calls
                ]
            
            # Add tool call results if present
            if message.tool_results:
                for tool_result in message.tool_results:
                    deepseek_messages.append({
                        "role": "tool",
                        "tool_call_id": tool_result.tool_call_id,
                        "content": tool_result.content
                    })
            
            deepseek_messages.append(deepseek_message)
        
        return deepseek_messages
    
    def _convert_role(self, role: MessageRole) -> str:
        """Convert internal role to Deepseek role.
        
        Args:
            role: Internal message role
            
        Returns:
            Deepseek role string
        """
        role_mapping = {
            MessageRole.USER: "user",
            MessageRole.ASSISTANT: "assistant",
            MessageRole.SYSTEM: "system",
            MessageRole.TOOL: "tool"
        }
        return role_mapping.get(role, "user")
    
    def _convert_tools(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert tools to Deepseek function format (OpenAI-compatible).
        
        Args:
            tools: Tool definitions
            
        Returns:
            Deepseek-formatted tools
        """
        deepseek_tools = []
        
        for tool in tools:
            deepseek_tool = {
                "type": "function",
                "function": {
                    "name": tool.get("name", ""),
                    "description": tool.get("description", ""),
                    "parameters": tool.get("parameters", {})
                }
            }
            deepseek_tools.append(deepseek_tool)
        
        return deepseek_tools
    
    def _get_model_name(self) -> str:
        """Get the actual model name for Deepseek API.
        
        Returns:
            Deepseek model name
        """
        return self.model_mapping.get(self.config.model, self.config.model)
    
    async def get_available_models(self) -> List[str]:
        """Get list of available models.
        
        Returns:
            List of available model names
        """
        try:
            response = await self.client.get("/models")
            if response.status_code == 200:
                data = response.json()
                models = [model["id"] for model in data.get("data", [])]
                return sorted(models)
        except Exception as e:
            logger.warning(f"Could not fetch Deepseek models: {e}")
        
        # Return default models
        return list(self.model_mapping.keys())
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information.
        
        Returns:
            Provider information dictionary
        """
        return {
            "name": "Deepseek",
            "type": "deepseek",
            "model": self.config.model,
            "supports_streaming": True,
            "supports_function_calling": True,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "timeout": self.config.timeout
        }
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.client.aclose()

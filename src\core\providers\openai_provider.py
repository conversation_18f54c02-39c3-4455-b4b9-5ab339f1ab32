"""
OpenAI Provider Implementation

OpenAI provider class with GPT models support, streaming responses, and function calling capabilities.
"""

import asyncio
from typing import AsyncGenerator, List, Dict, Any, Optional
import openai
from openai import AsyncOpenAI
from loguru import logger

from ..base import BaseProvider, ProviderConfig, Message, MessageRole, ToolCall, ToolResult
from ..logging import create_error, ErrorCode


class OpenAIProvider(BaseProvider):
    """OpenAI provider implementation."""
    
    def __init__(self, config: ProviderConfig):
        """Initialize OpenAI provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        
        # Initialize OpenAI client
        self.client = AsyncOpenAI(
            api_key=config.api_key,
            timeout=config.timeout or 30.0
        )
        
        # Model mapping for OpenAI
        self.model_mapping = {
            "gpt-4o": "gpt-4o",
            "gpt-4o-mini": "gpt-4o-mini",
            "gpt-4-turbo": "gpt-4-turbo-preview",
            "gpt-4": "gpt-4",
            "gpt-3.5-turbo": "gpt-3.5-turbo"
        }
        
        logger.info(f"OpenAI provider initialized with model: {config.model}")
    
    async def validate_connection(self) -> bool:
        """Validate connection to OpenAI API.
        
        Returns:
            True if connection is valid
        """
        try:
            # Test with a simple completion
            response = await self.client.chat.completions.create(
                model=self._get_model_name(),
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5,
                timeout=10.0
            )
            
            logger.debug("OpenAI connection validated successfully")
            return True
            
        except openai.AuthenticationError:
            logger.error("OpenAI authentication failed - invalid API key")
            return False
        except openai.RateLimitError:
            logger.warning("OpenAI rate limit reached during validation")
            return True  # API key is valid, just rate limited
        except Exception as e:
            logger.error(f"OpenAI connection validation failed: {e}")
            return False
    
    async def send_message(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """Send messages to OpenAI and get streaming response.
        
        Args:
            messages: List of conversation messages
            tools: Available tools for function calling
            stream: Whether to stream the response
            
        Yields:
            Response chunks as they arrive
        """
        try:
            # Convert messages to OpenAI format
            openai_messages = self._convert_messages(messages)
            
            # Prepare request parameters
            request_params = {
                "model": self._get_model_name(),
                "messages": openai_messages,
                "stream": stream,
                "timeout": self.config.timeout or 30.0
            }
            
            # Add optional parameters
            if self.config.max_tokens:
                request_params["max_tokens"] = self.config.max_tokens
            
            if self.config.temperature is not None:
                request_params["temperature"] = self.config.temperature
            
            # Add tools if provided
            if tools:
                request_params["tools"] = self._convert_tools(tools)
                request_params["tool_choice"] = "auto"
            
            logger.debug(f"Sending request to OpenAI: {len(openai_messages)} messages")
            
            if stream:
                async for chunk in self._stream_response(request_params):
                    yield chunk
            else:
                response = await self.client.chat.completions.create(**request_params)
                if response.choices and response.choices[0].message.content:
                    yield response.choices[0].message.content
                    
        except openai.AuthenticationError as e:
            logger.error(f"OpenAI authentication error: {e}")
            raise create_error(
                ErrorCode.AUTH_INVALID_API_KEY,
                "OpenAI API key is invalid or expired",
                cause=e
            )
        except openai.RateLimitError as e:
            logger.error(f"OpenAI rate limit exceeded: {e}")
            raise create_error(
                ErrorCode.PROVIDER_RATE_LIMIT,
                "OpenAI rate limit exceeded. Please try again later.",
                cause=e
            )
        except openai.APITimeoutError as e:
            logger.error(f"OpenAI request timeout: {e}")
            raise create_error(
                ErrorCode.PROVIDER_TIMEOUT,
                "OpenAI request timed out. Please try again.",
                cause=e
            )
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise create_error(
                ErrorCode.PROVIDER_API_ERROR,
                f"OpenAI API error: {e}",
                cause=e
            )
    
    async def _stream_response(self, request_params: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """Stream response from OpenAI.
        
        Args:
            request_params: Request parameters for OpenAI API
            
        Yields:
            Response chunks
        """
        try:
            stream = await self.client.chat.completions.create(**request_params)
            
            async for chunk in stream:
                if chunk.choices:
                    choice = chunk.choices[0]
                    
                    # Handle content chunks
                    if choice.delta and choice.delta.content:
                        yield choice.delta.content
                    
                    # Handle tool calls (function calling)
                    if choice.delta and choice.delta.tool_calls:
                        for tool_call in choice.delta.tool_calls:
                            if tool_call.function:
                                # Yield tool call information
                                tool_info = f"\n[Tool Call: {tool_call.function.name}]"
                                if tool_call.function.arguments:
                                    tool_info += f"\nArguments: {tool_call.function.arguments}"
                                yield tool_info
                    
                    # Handle finish reason
                    if choice.finish_reason:
                        logger.debug(f"OpenAI stream finished: {choice.finish_reason}")
                        
        except Exception as e:
            logger.error(f"Error streaming OpenAI response: {e}")
            raise
    
    def _convert_messages(self, messages: List[Message]) -> List[Dict[str, Any]]:
        """Convert internal messages to OpenAI format.
        
        Args:
            messages: Internal message format
            
        Returns:
            OpenAI-formatted messages
        """
        openai_messages = []
        
        for message in messages:
            openai_message = {
                "role": self._convert_role(message.role),
                "content": message.content
            }
            
            # Add tool calls if present
            if message.tool_calls:
                openai_message["tool_calls"] = [
                    {
                        "id": tool_call.id,
                        "type": "function",
                        "function": {
                            "name": tool_call.name,
                            "arguments": tool_call.arguments
                        }
                    }
                    for tool_call in message.tool_calls
                ]
            
            # Add tool call results if present
            if message.tool_results:
                for tool_result in message.tool_results:
                    openai_messages.append({
                        "role": "tool",
                        "tool_call_id": tool_result.tool_call_id,
                        "content": tool_result.content
                    })
            
            openai_messages.append(openai_message)
        
        return openai_messages
    
    def _convert_role(self, role: MessageRole) -> str:
        """Convert internal role to OpenAI role.
        
        Args:
            role: Internal message role
            
        Returns:
            OpenAI role string
        """
        role_mapping = {
            MessageRole.USER: "user",
            MessageRole.ASSISTANT: "assistant",
            MessageRole.SYSTEM: "system",
            MessageRole.TOOL: "tool"
        }
        return role_mapping.get(role, "user")
    
    def _convert_tools(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert tools to OpenAI function format.
        
        Args:
            tools: Tool definitions
            
        Returns:
            OpenAI-formatted tools
        """
        openai_tools = []
        
        for tool in tools:
            openai_tool = {
                "type": "function",
                "function": {
                    "name": tool.get("name", ""),
                    "description": tool.get("description", ""),
                    "parameters": tool.get("parameters", {})
                }
            }
            openai_tools.append(openai_tool)
        
        return openai_tools
    
    def _get_model_name(self) -> str:
        """Get the actual model name for OpenAI API.
        
        Returns:
            OpenAI model name
        """
        return self.model_mapping.get(self.config.model, self.config.model)
    
    async def get_available_models(self) -> List[str]:
        """Get list of available models.
        
        Returns:
            List of available model names
        """
        try:
            models = await self.client.models.list()
            # Filter for chat models
            chat_models = [
                model.id for model in models.data
                if "gpt" in model.id.lower() and "instruct" not in model.id.lower()
            ]
            return sorted(chat_models)
        except Exception as e:
            logger.warning(f"Could not fetch OpenAI models: {e}")
            # Return default models
            return list(self.model_mapping.keys())
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information.
        
        Returns:
            Provider information dictionary
        """
        return {
            "name": "OpenAI",
            "type": "openai",
            "model": self.config.model,
            "supports_streaming": True,
            "supports_function_calling": True,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "timeout": self.config.timeout
        }

"""
Secure API Key Storage

Encrypted local storage system for API keys using cryptography library with proper security practices.
"""

import os
import json
import base64
from pathlib import Path
from typing import Dict, Optional, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import keyring
from loguru import logger

from ..logging import create_error, ErrorCode, AppError
from ..base import ProviderType


class SecureStorage:
    """Secure storage for sensitive data like API keys."""
    
    def __init__(self, storage_dir: Optional[Path] = None):
        """Initialize secure storage.
        
        Args:
            storage_dir: Directory for storage files. Defaults to ~/.ai-cli-terminal/secure
        """
        self.storage_dir = storage_dir or Path.home() / ".ai-cli-terminal" / "secure"
        self.storage_file = self.storage_dir / "credentials.enc"
        self.service_name = "ai-cli-terminal"
        
        # Ensure storage directory exists with restricted permissions
        self.ensure_storage_dir()
        
        # Initialize encryption
        self._cipher = self._get_or_create_cipher()
        
        # Load existing data
        self._data: Dict[str, Any] = self._load_data()
    
    def ensure_storage_dir(self) -> None:
        """Ensure storage directory exists with proper permissions."""
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        
        # Set restrictive permissions (owner only)
        try:
            os.chmod(self.storage_dir, 0o700)
            logger.debug(f"Secure storage directory: {self.storage_dir}")
        except OSError as e:
            logger.warning(f"Could not set directory permissions: {e}")
    
    def _get_or_create_cipher(self) -> Fernet:
        """Get or create encryption cipher."""
        try:
            # Try to get master key from system keyring
            master_key = self._get_master_key_from_keyring()
            if master_key:
                return Fernet(master_key)
        except Exception as e:
            logger.warning(f"Could not retrieve master key from keyring: {e}")
        
        # Fallback: derive key from machine-specific data
        return self._create_cipher_from_machine_data()
    
    def _get_master_key_from_keyring(self) -> Optional[bytes]:
        """Get master encryption key from system keyring."""
        try:
            key_str = keyring.get_password(self.service_name, "master-key")
            if key_str:
                return base64.urlsafe_b64decode(key_str.encode())
            
            # Generate new master key
            master_key = Fernet.generate_key()
            key_str = base64.urlsafe_b64encode(master_key).decode()
            keyring.set_password(self.service_name, "master-key", key_str)
            logger.info("New master key generated and stored in keyring")
            return master_key
            
        except Exception as e:
            logger.warning(f"Keyring operation failed: {e}")
            return None
    
    def _create_cipher_from_machine_data(self) -> Fernet:
        """Create cipher from machine-specific data as fallback."""
        logger.warning("Using machine-specific encryption (less secure than keyring)")
        
        # Use machine-specific data for key derivation
        machine_data = self._get_machine_identifier()
        salt = b"ai-cli-terminal-salt"  # Fixed salt for consistency
        
        # Derive key using PBKDF2
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(machine_data.encode()))
        
        return Fernet(key)
    
    def _get_machine_identifier(self) -> str:
        """Get machine-specific identifier for key derivation."""
        # Combine multiple machine-specific attributes
        identifiers = []
        
        # User home directory path
        identifiers.append(str(Path.home()))
        
        # Username
        identifiers.append(os.getenv('USERNAME', os.getenv('USER', 'unknown')))
        
        # Machine name
        identifiers.append(os.getenv('COMPUTERNAME', os.getenv('HOSTNAME', 'unknown')))
        
        # Create consistent identifier
        machine_id = "|".join(identifiers)
        logger.debug("Machine identifier created for encryption")
        
        return machine_id
    
    def _load_data(self) -> Dict[str, Any]:
        """Load encrypted data from storage file."""
        if not self.storage_file.exists():
            return {}
        
        try:
            with open(self.storage_file, 'rb') as f:
                encrypted_data = f.read()
            
            if not encrypted_data:
                return {}
            
            # Decrypt data
            decrypted_data = self._cipher.decrypt(encrypted_data)
            data = json.loads(decrypted_data.decode())
            
            logger.debug("Secure storage data loaded")
            return data
            
        except Exception as e:
            logger.error(f"Failed to load secure storage: {e}")
            # Return empty dict and backup corrupted file
            if self.storage_file.exists():
                backup_file = self.storage_file.with_suffix('.enc.backup')
                self.storage_file.rename(backup_file)
                logger.warning(f"Corrupted storage backed up to: {backup_file}")
            
            return {}
    
    def _save_data(self) -> None:
        """Save encrypted data to storage file."""
        try:
            # Serialize data
            json_data = json.dumps(self._data, indent=2)
            
            # Encrypt data
            encrypted_data = self._cipher.encrypt(json_data.encode())
            
            # Write to file with atomic operation
            temp_file = self.storage_file.with_suffix('.tmp')
            with open(temp_file, 'wb') as f:
                f.write(encrypted_data)
            
            # Set restrictive permissions
            os.chmod(temp_file, 0o600)
            
            # Atomic rename
            temp_file.replace(self.storage_file)
            
            logger.debug("Secure storage data saved")
            
        except Exception as e:
            logger.error(f"Failed to save secure storage: {e}")
            raise create_error(
                ErrorCode.AUTH_ENCRYPTION_ERROR,
                f"Failed to save encrypted data: {e}",
                cause=e
            )
    
    def store_api_key(self, provider_type: ProviderType, api_key: str) -> None:
        """Store API key for a provider.
        
        Args:
            provider_type: The AI provider type
            api_key: The API key to store
        """
        if not api_key:
            raise create_error(
                ErrorCode.AUTH_INVALID_API_KEY,
                "API key cannot be empty"
            )
        
        # Store in data structure
        if 'api_keys' not in self._data:
            self._data['api_keys'] = {}
        
        self._data['api_keys'][provider_type.value] = api_key
        
        # Save to encrypted storage
        self._save_data()
        
        logger.info(f"API key stored for provider: {provider_type.value}")
    
    def get_api_key(self, provider_type: ProviderType) -> Optional[str]:
        """Get API key for a provider.
        
        Args:
            provider_type: The AI provider type
            
        Returns:
            The API key if found, None otherwise
        """
        api_keys = self._data.get('api_keys', {})
        api_key = api_keys.get(provider_type.value)
        
        if api_key:
            logger.debug(f"API key retrieved for provider: {provider_type.value}")
        
        return api_key
    
    def remove_api_key(self, provider_type: ProviderType) -> bool:
        """Remove API key for a provider.
        
        Args:
            provider_type: The AI provider type
            
        Returns:
            True if key was removed, False if not found
        """
        api_keys = self._data.get('api_keys', {})
        
        if provider_type.value in api_keys:
            del api_keys[provider_type.value]
            self._save_data()
            logger.info(f"API key removed for provider: {provider_type.value}")
            return True
        
        return False
    
    def list_stored_providers(self) -> list[ProviderType]:
        """List providers that have stored API keys.
        
        Returns:
            List of provider types with stored keys
        """
        api_keys = self._data.get('api_keys', {})
        providers = []
        
        for provider_name in api_keys.keys():
            try:
                provider_type = ProviderType(provider_name)
                providers.append(provider_type)
            except ValueError:
                logger.warning(f"Unknown provider in storage: {provider_name}")
        
        return providers
    
    def store_setting(self, key: str, value: Any) -> None:
        """Store a general setting.
        
        Args:
            key: Setting key
            value: Setting value (must be JSON serializable)
        """
        if 'settings' not in self._data:
            self._data['settings'] = {}
        
        self._data['settings'][key] = value
        self._save_data()
        
        logger.debug(f"Setting stored: {key}")
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a general setting.
        
        Args:
            key: Setting key
            default: Default value if not found
            
        Returns:
            Setting value or default
        """
        settings = self._data.get('settings', {})
        return settings.get(key, default)
    
    def remove_setting(self, key: str) -> bool:
        """Remove a setting.
        
        Args:
            key: Setting key
            
        Returns:
            True if setting was removed, False if not found
        """
        settings = self._data.get('settings', {})
        
        if key in settings:
            del settings[key]
            self._save_data()
            logger.debug(f"Setting removed: {key}")
            return True
        
        return False
    
    def clear_all_data(self) -> None:
        """Clear all stored data."""
        self._data = {}
        self._save_data()
        logger.warning("All secure storage data cleared")
    
    def export_data(self, include_api_keys: bool = False) -> Dict[str, Any]:
        """Export data for backup (optionally excluding API keys).
        
        Args:
            include_api_keys: Whether to include API keys in export
            
        Returns:
            Dictionary with exportable data
        """
        export_data = self._data.copy()
        
        if not include_api_keys and 'api_keys' in export_data:
            # Replace API keys with placeholders
            api_keys = export_data['api_keys']
            for provider in api_keys:
                api_keys[provider] = "***REDACTED***"
        
        return export_data
    
    def get_storage_info(self) -> Dict[str, Any]:
        """Get information about the storage system.
        
        Returns:
            Dictionary with storage information
        """
        return {
            "storage_dir": str(self.storage_dir),
            "storage_file": str(self.storage_file),
            "file_exists": self.storage_file.exists(),
            "file_size": self.storage_file.stat().st_size if self.storage_file.exists() else 0,
            "stored_providers": len(self._data.get('api_keys', {})),
            "stored_settings": len(self._data.get('settings', {})),
            "encryption_method": "Fernet (AES 128)",
            "keyring_available": self._is_keyring_available()
        }
    
    def _is_keyring_available(self) -> bool:
        """Check if system keyring is available."""
        try:
            keyring.get_keyring()
            return True
        except Exception:
            return False

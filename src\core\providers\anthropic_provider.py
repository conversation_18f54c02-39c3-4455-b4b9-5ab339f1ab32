"""
Anthropic Provider Implementation

Anthropic provider class with Claude models support, streaming responses, and tool calling capabilities.
"""

import asyncio
from typing import AsyncGenerator, List, Dict, Any, Optional
import anthropic
from anthropic import AsyncAnthropic
from loguru import logger

from ..base import BaseProvider, ProviderConfig, Message, MessageRole, ToolCall, <PERSON>lR<PERSON>ult
from ..logging import create_error, ErrorCode


class AnthropicProvider(BaseProvider):
    """Anthropic provider implementation."""
    
    def __init__(self, config: ProviderConfig):
        """Initialize Anthropic provider.
        
        Args:
            config: Provider configuration
        """
        super().__init__(config)
        
        # Initialize Anthropic client
        self.client = AsyncAnthropic(
            api_key=config.api_key,
            timeout=config.timeout or 30.0
        )
        
        # Model mapping for Anthropic
        self.model_mapping = {
            "claude-3-5-sonnet-20241022": "claude-3-5-sonnet-20241022",
            "claude-3-5-haiku-20241022": "claude-3-5-haiku-20241022",
            "claude-3-opus-20240229": "claude-3-opus-20240229",
            "claude-3-sonnet-20240229": "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307": "claude-3-haiku-20240307"
        }
        
        logger.info(f"Anthropic provider initialized with model: {config.model}")
    
    async def validate_connection(self) -> bool:
        """Validate connection to Anthropic API.
        
        Returns:
            True if connection is valid
        """
        try:
            # Test with a simple completion
            response = await self.client.messages.create(
                model=self._get_model_name(),
                max_tokens=5,
                messages=[{"role": "user", "content": "Hello"}],
                timeout=10.0
            )
            
            logger.debug("Anthropic connection validated successfully")
            return True
            
        except anthropic.AuthenticationError:
            logger.error("Anthropic authentication failed - invalid API key")
            return False
        except anthropic.RateLimitError:
            logger.warning("Anthropic rate limit reached during validation")
            return True  # API key is valid, just rate limited
        except Exception as e:
            logger.error(f"Anthropic connection validation failed: {e}")
            return False
    
    async def send_message(
        self,
        messages: List[Message],
        tools: Optional[List[Dict[str, Any]]] = None,
        stream: bool = True
    ) -> AsyncGenerator[str, None]:
        """Send messages to Anthropic and get streaming response.
        
        Args:
            messages: List of conversation messages
            tools: Available tools for function calling
            stream: Whether to stream the response
            
        Yields:
            Response chunks as they arrive
        """
        try:
            # Convert messages to Anthropic format
            anthropic_messages, system_message = self._convert_messages(messages)
            
            # Prepare request parameters
            request_params = {
                "model": self._get_model_name(),
                "messages": anthropic_messages,
                "stream": stream,
                "timeout": self.config.timeout or 30.0
            }
            
            # Add system message if present
            if system_message:
                request_params["system"] = system_message
            
            # Add max_tokens (required for Anthropic)
            request_params["max_tokens"] = self.config.max_tokens or 4096
            
            # Add optional parameters
            if self.config.temperature is not None:
                request_params["temperature"] = self.config.temperature
            
            # Add tools if provided
            if tools:
                request_params["tools"] = self._convert_tools(tools)
            
            logger.debug(f"Sending request to Anthropic: {len(anthropic_messages)} messages")
            
            if stream:
                async for chunk in self._stream_response(request_params):
                    yield chunk
            else:
                response = await self.client.messages.create(**request_params)
                if response.content:
                    for content_block in response.content:
                        if content_block.type == "text":
                            yield content_block.text
                        elif content_block.type == "tool_use":
                            tool_info = f"\n[Tool Call: {content_block.name}]"
                            if content_block.input:
                                tool_info += f"\nArguments: {content_block.input}"
                            yield tool_info
                    
        except anthropic.AuthenticationError as e:
            logger.error(f"Anthropic authentication error: {e}")
            raise create_error(
                ErrorCode.AUTH_INVALID_API_KEY,
                "Anthropic API key is invalid or expired",
                cause=e
            )
        except anthropic.RateLimitError as e:
            logger.error(f"Anthropic rate limit exceeded: {e}")
            raise create_error(
                ErrorCode.PROVIDER_RATE_LIMIT,
                "Anthropic rate limit exceeded. Please try again later.",
                cause=e
            )
        except anthropic.APITimeoutError as e:
            logger.error(f"Anthropic request timeout: {e}")
            raise create_error(
                ErrorCode.PROVIDER_TIMEOUT,
                "Anthropic request timed out. Please try again.",
                cause=e
            )
        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            raise create_error(
                ErrorCode.PROVIDER_API_ERROR,
                f"Anthropic API error: {e}",
                cause=e
            )
    
    async def _stream_response(self, request_params: Dict[str, Any]) -> AsyncGenerator[str, None]:
        """Stream response from Anthropic.
        
        Args:
            request_params: Request parameters for Anthropic API
            
        Yields:
            Response chunks
        """
        try:
            async with self.client.messages.stream(**request_params) as stream:
                async for event in stream:
                    if event.type == "content_block_delta":
                        if event.delta.type == "text_delta":
                            yield event.delta.text
                        elif event.delta.type == "input_json_delta":
                            # Handle tool call input streaming
                            yield event.delta.partial_json
                    
                    elif event.type == "content_block_start":
                        if event.content_block.type == "tool_use":
                            # Start of tool call
                            tool_info = f"\n[Tool Call: {event.content_block.name}]"
                            yield tool_info
                    
                    elif event.type == "message_stop":
                        logger.debug("Anthropic stream finished")
                        
        except Exception as e:
            logger.error(f"Error streaming Anthropic response: {e}")
            raise
    
    def _convert_messages(self, messages: List[Message]) -> tuple[List[Dict[str, Any]], Optional[str]]:
        """Convert internal messages to Anthropic format.
        
        Args:
            messages: Internal message format
            
        Returns:
            Tuple of (Anthropic-formatted messages, system message)
        """
        anthropic_messages = []
        system_message = None
        
        for message in messages:
            if message.role == MessageRole.SYSTEM:
                # Anthropic handles system messages separately
                system_message = message.content
                continue
            
            # Convert role
            role = self._convert_role(message.role)
            
            # Handle content
            content = []
            
            # Add text content
            if message.content:
                content.append({
                    "type": "text",
                    "text": message.content
                })
            
            # Add tool calls if present
            if message.tool_calls:
                for tool_call in message.tool_calls:
                    content.append({
                        "type": "tool_use",
                        "id": tool_call.id,
                        "name": tool_call.name,
                        "input": tool_call.arguments
                    })
            
            # Add tool results if present
            if message.tool_results:
                for tool_result in message.tool_results:
                    content.append({
                        "type": "tool_result",
                        "tool_use_id": tool_result.tool_call_id,
                        "content": tool_result.content
                    })
            
            anthropic_message = {
                "role": role,
                "content": content if content else message.content
            }
            
            anthropic_messages.append(anthropic_message)
        
        return anthropic_messages, system_message
    
    def _convert_role(self, role: MessageRole) -> str:
        """Convert internal role to Anthropic role.
        
        Args:
            role: Internal message role
            
        Returns:
            Anthropic role string
        """
        role_mapping = {
            MessageRole.USER: "user",
            MessageRole.ASSISTANT: "assistant",
            MessageRole.TOOL: "user"  # Tool results are sent as user messages
        }
        return role_mapping.get(role, "user")
    
    def _convert_tools(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Convert tools to Anthropic tool format.
        
        Args:
            tools: Tool definitions
            
        Returns:
            Anthropic-formatted tools
        """
        anthropic_tools = []
        
        for tool in tools:
            anthropic_tool = {
                "name": tool.get("name", ""),
                "description": tool.get("description", ""),
                "input_schema": tool.get("parameters", {})
            }
            anthropic_tools.append(anthropic_tool)
        
        return anthropic_tools
    
    def _get_model_name(self) -> str:
        """Get the actual model name for Anthropic API.
        
        Returns:
            Anthropic model name
        """
        return self.model_mapping.get(self.config.model, self.config.model)
    
    async def get_available_models(self) -> List[str]:
        """Get list of available models.
        
        Returns:
            List of available model names
        """
        # Anthropic doesn't have a models endpoint, return known models
        return list(self.model_mapping.keys())
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information.
        
        Returns:
            Provider information dictionary
        """
        return {
            "name": "Anthropic",
            "type": "anthropic",
            "model": self.config.model,
            "supports_streaming": True,
            "supports_function_calling": True,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "timeout": self.config.timeout
        }

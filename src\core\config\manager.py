"""
Configuration Manager

Handles loading, saving, and managing application configuration including user settings,
API keys, and provider preferences.
"""

import os
import json
import toml
from pathlib import Path
from typing import Dict, Optional, Any
from cryptography.fernet import Fernet
import keyring
from loguru import logger

from ..base import AppConfig, ProviderConfig, ProviderType
from ..logging import create_error, ErrorCode
from ..auth.storage import SecureStorage


class ConfigManager:
    """Manages application configuration and secure storage."""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """Initialize configuration manager.

        Args:
            config_dir: Directory to store configuration files.
                       Defaults to ~/.ai-cli-terminal
        """
        self.config_dir = config_dir or Path.home() / ".ai-cli-terminal"
        self.config_file = self.config_dir / "config.toml"
        self.ensure_config_dir()

        # Initialize secure storage for API keys
        self.secure_storage = SecureStorage(self.config_dir / "secure")

        # Initialize encryption for sensitive data (legacy support)
        self._encryption_key = self._get_or_create_encryption_key()
        self._cipher = Fernet(self._encryption_key)

        # Load configuration
        self._config: Optional[AppConfig] = None
        self.load_config()
    
    def ensure_config_dir(self) -> None:
        """Ensure configuration directory exists."""
        self.config_dir.mkdir(parents=True, exist_ok=True)
        logger.debug(f"Configuration directory: {self.config_dir}")
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for sensitive data."""
        service_name = "ai-cli-terminal"
        key_name = "encryption-key"
        
        try:
            # Try to get existing key from keyring
            key_str = keyring.get_password(service_name, key_name)
            if key_str:
                return key_str.encode()
        except Exception as e:
            logger.warning(f"Could not retrieve encryption key from keyring: {e}")
        
        # Generate new key
        key = Fernet.generate_key()
        
        try:
            # Store key in keyring
            keyring.set_password(service_name, key_name, key.decode())
            logger.debug("Encryption key stored in system keyring")
        except Exception as e:
            logger.warning(f"Could not store encryption key in keyring: {e}")
            # Fallback: store in config directory (less secure)
            key_file = self.config_dir / ".encryption_key"
            key_file.write_bytes(key)
            key_file.chmod(0o600)  # Restrict permissions
            logger.warning(f"Encryption key stored in file: {key_file}")
        
        return key
    
    def encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data."""
        return self._cipher.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data."""
        return self._cipher.decrypt(encrypted_data.encode()).decode()
    
    def load_config(self) -> AppConfig:
        """Load configuration from file."""
        if not self.config_file.exists():
            logger.info("Configuration file not found, creating default configuration")
            self._config = AppConfig()
            self.save_config()
            return self._config
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = toml.load(f)
            
            # Convert provider configurations
            providers = {}
            for provider_name, provider_data in config_data.get('providers', {}).items():
                try:
                    provider_type = ProviderType(provider_name)
                    # Decrypt API key if present
                    if 'api_key' in provider_data and provider_data['api_key']:
                        provider_data['api_key'] = self.decrypt_data(provider_data['api_key'])
                    
                    providers[provider_type] = ProviderConfig(
                        provider_type=provider_type,
                        **provider_data
                    )
                except (ValueError, Exception) as e:
                    logger.warning(f"Invalid provider configuration for {provider_name}: {e}")
            
            # Create main config
            config_data['providers'] = providers
            if 'current_provider' in config_data:
                config_data['current_provider'] = ProviderType(config_data['current_provider'])
            
            self._config = AppConfig(**config_data)
            logger.info("Configuration loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            logger.info("Using default configuration")
            self._config = AppConfig()
            self.save_config()
        
        return self._config
    
    def save_config(self) -> None:
        """Save configuration to file."""
        if not self._config:
            return
        
        try:
            # Prepare config data for serialization
            config_data = self._config.model_dump()
            
            # Encrypt API keys and convert provider configs
            providers = {}
            for provider_type, provider_config in config_data['providers'].items():
                provider_data = provider_config.copy()
                if 'api_key' in provider_data and provider_data['api_key']:
                    provider_data['api_key'] = self.encrypt_data(provider_data['api_key'])
                
                # Convert enum to string
                provider_name = provider_type.value if hasattr(provider_type, 'value') else str(provider_type)
                providers[provider_name] = provider_data
            
            config_data['providers'] = providers
            config_data['current_provider'] = self._config.current_provider.value
            
            # Save to file
            with open(self.config_file, 'w', encoding='utf-8') as f:
                toml.dump(config_data, f)
            
            logger.debug("Configuration saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
    
    @property
    def config(self) -> AppConfig:
        """Get current configuration."""
        if not self._config:
            self._config = self.load_config()
        return self._config
    
    def update_provider_config(self, provider_type: ProviderType, config: ProviderConfig) -> None:
        """Update provider configuration."""
        # Store API key securely
        if config.api_key:
            self.secure_storage.store_api_key(provider_type, config.api_key)

        # Store config without API key (it's in secure storage)
        config_without_key = ProviderConfig(
            provider_type=config.provider_type,
            api_key="",  # Don't store in config file
            model=config.model,
            max_tokens=config.max_tokens,
            temperature=config.temperature,
            timeout=config.timeout
        )

        self._config.providers[provider_type] = config_without_key
        self.save_config()
        logger.info(f"Updated configuration for provider: {provider_type.value}")
    
    def set_current_provider(self, provider_type: ProviderType) -> None:
        """Set the current active provider."""
        if provider_type not in self._config.providers:
            raise ValueError(f"Provider {provider_type.value} is not configured")
        
        self._config.current_provider = provider_type
        self.save_config()
        logger.info(f"Switched to provider: {provider_type.value}")
    
    def get_current_provider_config(self) -> Optional[ProviderConfig]:
        """Get configuration for the current provider."""
        provider_config = self._config.providers.get(self._config.current_provider)
        if provider_config:
            # Get API key from secure storage
            api_key = self.secure_storage.get_api_key(self._config.current_provider)
            if api_key:
                return ProviderConfig(
                    provider_type=provider_config.provider_type,
                    api_key=api_key,
                    model=provider_config.model,
                    max_tokens=provider_config.max_tokens,
                    temperature=provider_config.temperature,
                    timeout=provider_config.timeout
                )
        return None
    
    def is_provider_configured(self, provider_type: ProviderType) -> bool:
        """Check if a provider is configured."""
        provider_config = self._config.providers.get(provider_type)
        if provider_config is None:
            return False

        # Check if API key exists in secure storage
        api_key = self.secure_storage.get_api_key(provider_type)
        return bool(api_key) and bool(provider_config.model)
    
    def remove_provider_config(self, provider_type: ProviderType) -> None:
        """Remove provider configuration."""
        # Remove from secure storage
        self.secure_storage.remove_api_key(provider_type)

        # Remove from config
        if provider_type in self._config.providers:
            del self._config.providers[provider_type]

            # If this was the current provider, switch to another one
            if self._config.current_provider == provider_type:
                available_providers = list(self._config.providers.keys())
                if available_providers:
                    self._config.current_provider = available_providers[0]
                else:
                    self._config.current_provider = ProviderType.OPENAI

            self.save_config()
            logger.info(f"Removed configuration for provider: {provider_type.value}")
    
    def export_config(self, file_path: Path, include_api_keys: bool = False) -> None:
        """Export configuration to a file."""
        config_data = self._config.model_dump()
        
        if not include_api_keys:
            # Remove API keys from export
            for provider_config in config_data['providers'].values():
                if 'api_key' in provider_config:
                    provider_config['api_key'] = "***REDACTED***"
        
        with open(file_path, 'w', encoding='utf-8') as f:
            if file_path.suffix.lower() == '.json':
                json.dump(config_data, f, indent=2, default=str)
            else:
                toml.dump(config_data, f)
        
        logger.info(f"Configuration exported to: {file_path}")
    
    def reset_config(self) -> None:
        """Reset configuration to defaults."""
        self._config = AppConfig()
        self.save_config()
        logger.info("Configuration reset to defaults")

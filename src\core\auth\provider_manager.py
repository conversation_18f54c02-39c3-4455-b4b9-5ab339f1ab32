"""
Provider Configuration Management

System for managing multiple provider configurations, model selection, and switching between providers.
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from loguru import logger

from ..base import ProviderType, ProviderConfig
from ..config.manager import ConfigManager
from .storage import SecureStorage
from ..logging import create_error, ErrorCode
from ..providers import ProviderFactory


@dataclass
class ProviderInfo:
    """Information about a provider."""
    provider_type: ProviderType
    name: str
    description: str
    website: str
    api_docs: str
    default_models: List[str]
    supports_streaming: bool = True
    supports_function_calling: bool = True


class ProviderManager:
    """Manages AI provider configurations and operations."""
    
    # Provider information database
    PROVIDER_INFO = {
        ProviderType.OPENAI: ProviderInfo(
            provider_type=ProviderType.OPENAI,
            name="OpenAI",
            description="Advanced AI models including GPT-4 and GPT-3.5",
            website="https://openai.com",
            api_docs="https://platform.openai.com/docs",
            default_models=["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo"],
            supports_streaming=True,
            supports_function_calling=True
        ),
        ProviderType.ANTHROPIC: ProviderInfo(
            provider_type=ProviderType.ANTHROPIC,
            name="Anthropic",
            description="Claude models focused on safety and helpfulness",
            website="https://anthropic.com",
            api_docs="https://docs.anthropic.com",
            default_models=["claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", 
                          "claude-3-opus-20240229", "claude-3-sonnet-20240229"],
            supports_streaming=True,
            supports_function_calling=True
        ),
        ProviderType.GOOGLE: ProviderInfo(
            provider_type=ProviderType.GOOGLE,
            name="Google",
            description="Gemini models with multimodal capabilities",
            website="https://ai.google.dev",
            api_docs="https://ai.google.dev/docs",
            default_models=["gemini-1.5-pro", "gemini-1.5-flash", "gemini-1.0-pro"],
            supports_streaming=True,
            supports_function_calling=True
        ),
        ProviderType.DEEPSEEK: ProviderInfo(
            provider_type=ProviderType.DEEPSEEK,
            name="DeepSeek",
            description="Advanced reasoning and coding models",
            website="https://deepseek.com",
            api_docs="https://platform.deepseek.com/api-docs",
            default_models=["deepseek-chat", "deepseek-coder"],
            supports_streaming=True,
            supports_function_calling=True
        )
    }
    
    def __init__(self, config_manager: ConfigManager, secure_storage: SecureStorage):
        """Initialize provider manager.
        
        Args:
            config_manager: Configuration manager instance
            secure_storage: Secure storage instance
        """
        self.config_manager = config_manager
        self.secure_storage = secure_storage
    
    def get_provider_info(self, provider_type: ProviderType) -> ProviderInfo:
        """Get information about a provider.
        
        Args:
            provider_type: The provider type
            
        Returns:
            Provider information
        """
        return self.PROVIDER_INFO[provider_type]
    
    def get_all_provider_info(self) -> Dict[ProviderType, ProviderInfo]:
        """Get information about all providers.
        
        Returns:
            Dictionary mapping provider types to their information
        """
        return self.PROVIDER_INFO.copy()
    
    def is_provider_configured(self, provider_type: ProviderType) -> bool:
        """Check if a provider is fully configured.
        
        Args:
            provider_type: The provider type
            
        Returns:
            True if provider is configured with valid API key and model
        """
        # Check if provider config exists
        config = self.config_manager.config
        if provider_type not in config.providers:
            return False
        
        provider_config = config.providers[provider_type]
        
        # Check if API key exists
        api_key = self.secure_storage.get_api_key(provider_type)
        if not api_key:
            return False
        
        # Check if model is set
        if not provider_config.model:
            return False
        
        return True
    
    def get_configured_providers(self) -> List[ProviderType]:
        """Get list of configured providers.
        
        Returns:
            List of provider types that are fully configured
        """
        return [
            provider_type for provider_type in ProviderType
            if self.is_provider_configured(provider_type)
        ]
    
    def get_current_provider(self) -> Optional[ProviderType]:
        """Get the currently active provider.
        
        Returns:
            Current provider type if configured, None otherwise
        """
        current = self.config_manager.config.current_provider
        if self.is_provider_configured(current):
            return current
        
        # Current provider not configured, try to find another one
        configured = self.get_configured_providers()
        if configured:
            # Switch to first configured provider
            self.set_current_provider(configured[0])
            return configured[0]
        
        return None
    
    def set_current_provider(self, provider_type: ProviderType) -> None:
        """Set the current active provider.
        
        Args:
            provider_type: The provider type to set as current
            
        Raises:
            AppError: If provider is not configured
        """
        if not self.is_provider_configured(provider_type):
            raise create_error(
                ErrorCode.PROVIDER_NOT_CONFIGURED,
                f"Provider {provider_type.value} is not configured"
            )
        
        self.config_manager.set_current_provider(provider_type)
        logger.info(f"Current provider set to: {provider_type.value}")
    
    def configure_provider(
        self,
        provider_type: ProviderType,
        api_key: str,
        model: str,
        max_tokens: Optional[int] = None,
        temperature: Optional[float] = None,
        timeout: Optional[int] = None
    ) -> None:
        """Configure a provider with all necessary settings.
        
        Args:
            provider_type: The provider type
            api_key: API key for the provider
            model: Model name to use
            max_tokens: Maximum tokens in response
            temperature: Temperature for response generation
            timeout: Request timeout in seconds
        """
        # Validate inputs
        if not api_key.strip():
            raise create_error(
                ErrorCode.AUTH_INVALID_API_KEY,
                "API key cannot be empty"
            )
        
        if not model.strip():
            raise create_error(
                ErrorCode.CONFIG_VALIDATION_ERROR,
                "Model name cannot be empty"
            )
        
        # Store API key securely
        self.secure_storage.store_api_key(provider_type, api_key.strip())
        
        # Create provider configuration
        provider_config = ProviderConfig(
            provider_type=provider_type,
            api_key=api_key.strip(),  # This will be encrypted by config manager
            model=model.strip(),
            max_tokens=max_tokens,
            temperature=temperature,
            timeout=timeout or 30
        )
        
        # Save configuration
        self.config_manager.update_provider_config(provider_type, provider_config)
        
        logger.info(f"Provider configured: {provider_type.value} with model {model}")
    
    def update_provider_model(self, provider_type: ProviderType, model: str) -> None:
        """Update the model for a configured provider.
        
        Args:
            provider_type: The provider type
            model: New model name
            
        Raises:
            AppError: If provider is not configured
        """
        if not self.is_provider_configured(provider_type):
            raise create_error(
                ErrorCode.PROVIDER_NOT_CONFIGURED,
                f"Provider {provider_type.value} is not configured"
            )
        
        config = self.config_manager.config
        provider_config = config.providers[provider_type]
        
        # Update model
        updated_config = ProviderConfig(
            provider_type=provider_config.provider_type,
            api_key=provider_config.api_key,
            model=model.strip(),
            max_tokens=provider_config.max_tokens,
            temperature=provider_config.temperature,
            timeout=provider_config.timeout
        )
        
        self.config_manager.update_provider_config(provider_type, updated_config)
        logger.info(f"Model updated for {provider_type.value}: {model}")
    
    def remove_provider(self, provider_type: ProviderType) -> None:
        """Remove a provider configuration.
        
        Args:
            provider_type: The provider type to remove
        """
        # Remove from secure storage
        self.secure_storage.remove_api_key(provider_type)
        
        # Remove from configuration
        self.config_manager.remove_provider_config(provider_type)
        
        logger.info(f"Provider removed: {provider_type.value}")
    
    def get_provider_config(self, provider_type: ProviderType) -> Optional[ProviderConfig]:
        """Get configuration for a provider.
        
        Args:
            provider_type: The provider type
            
        Returns:
            Provider configuration if exists, None otherwise
        """
        config = self.config_manager.config
        provider_config = config.providers.get(provider_type)
        
        if provider_config:
            # Get API key from secure storage
            api_key = self.secure_storage.get_api_key(provider_type)
            if api_key:
                # Create config with actual API key
                return ProviderConfig(
                    provider_type=provider_config.provider_type,
                    api_key=api_key,
                    model=provider_config.model,
                    max_tokens=provider_config.max_tokens,
                    temperature=provider_config.temperature,
                    timeout=provider_config.timeout
                )
        
        return None
    
    def get_current_provider_config(self) -> Optional[ProviderConfig]:
        """Get configuration for the current provider.
        
        Returns:
            Current provider configuration if available, None otherwise
        """
        current_provider = self.get_current_provider()
        if current_provider:
            return self.get_provider_config(current_provider)
        return None
    
    def get_available_models(self, provider_type: ProviderType) -> List[str]:
        """Get available models for a provider.
        
        Args:
            provider_type: The provider type
            
        Returns:
            List of available model names
        """
        provider_info = self.get_provider_info(provider_type)
        return provider_info.default_models.copy()
    
    def validate_model(self, provider_type: ProviderType, model: str) -> bool:
        """Validate if a model is available for a provider.
        
        Args:
            provider_type: The provider type
            model: Model name to validate
            
        Returns:
            True if model is valid for the provider
        """
        available_models = self.get_available_models(provider_type)
        return model in available_models
    
    def get_provider_status(self) -> Dict[ProviderType, Dict[str, any]]:
        """Get status information for all providers.
        
        Returns:
            Dictionary with status information for each provider
        """
        status = {}
        
        for provider_type in ProviderType:
            provider_info = self.get_provider_info(provider_type)
            is_configured = self.is_provider_configured(provider_type)
            is_current = provider_type == self.config_manager.config.current_provider
            
            provider_config = None
            if is_configured:
                provider_config = self.get_provider_config(provider_type)
            
            status[provider_type] = {
                "name": provider_info.name,
                "description": provider_info.description,
                "is_configured": is_configured,
                "is_current": is_current,
                "model": provider_config.model if provider_config else None,
                "max_tokens": provider_config.max_tokens if provider_config else None,
                "temperature": provider_config.temperature if provider_config else None,
                "supports_streaming": provider_info.supports_streaming,
                "supports_function_calling": provider_info.supports_function_calling,
                "available_models": provider_info.default_models
            }
        
        return status
    
    def export_configuration(self, include_api_keys: bool = False) -> Dict[str, any]:
        """Export provider configurations.
        
        Args:
            include_api_keys: Whether to include API keys in export
            
        Returns:
            Dictionary with exportable configuration data
        """
        export_data = {
            "providers": {},
            "current_provider": self.config_manager.config.current_provider.value,
            "provider_info": {}
        }
        
        # Export provider configurations
        for provider_type in ProviderType:
            if self.is_provider_configured(provider_type):
                config = self.get_provider_config(provider_type)
                if config:
                    provider_data = {
                        "model": config.model,
                        "max_tokens": config.max_tokens,
                        "temperature": config.temperature,
                        "timeout": config.timeout
                    }
                    
                    if include_api_keys:
                        provider_data["api_key"] = config.api_key
                    else:
                        provider_data["api_key"] = "***REDACTED***"
                    
                    export_data["providers"][provider_type.value] = provider_data
        
        # Export provider information
        for provider_type, info in self.PROVIDER_INFO.items():
            export_data["provider_info"][provider_type.value] = {
                "name": info.name,
                "description": info.description,
                "website": info.website,
                "api_docs": info.api_docs,
                "default_models": info.default_models,
                "supports_streaming": info.supports_streaming,
                "supports_function_calling": info.supports_function_calling
            }
        
        return export_data

    async def validate_provider_connection(self, provider_type: ProviderType) -> bool:
        """Validate connection to a provider.

        Args:
            provider_type: The provider type to validate

        Returns:
            True if connection is valid

        Raises:
            AppError: If provider is not configured
        """
        if not self.is_provider_configured(provider_type):
            raise create_error(
                ErrorCode.PROVIDER_NOT_CONFIGURED,
                f"Provider {provider_type.value} is not configured"
            )

        try:
            # Get provider configuration
            config = self.get_provider_config(provider_type)
            if not config:
                return False

            # Create provider instance
            provider = ProviderFactory.create_provider(config)

            # Validate connection
            is_valid = await provider.validate_connection()

            if is_valid:
                logger.info(f"Provider {provider_type.value} connection validated successfully")
            else:
                logger.warning(f"Provider {provider_type.value} connection validation failed")

            return is_valid

        except Exception as e:
            logger.error(f"Error validating provider {provider_type.value}: {e}")
            return False

    async def validate_current_provider(self) -> bool:
        """Validate connection to the current provider.

        Returns:
            True if current provider connection is valid
        """
        current_provider = self.get_current_provider()
        if not current_provider:
            logger.warning("No current provider configured")
            return False

        return await self.validate_provider_connection(current_provider)

    async def test_provider_with_credentials(
        self,
        provider_type: ProviderType,
        api_key: str,
        model: str
    ) -> bool:
        """Test provider connection with given credentials without saving them.

        Args:
            provider_type: The provider type
            api_key: API key to test
            model: Model to test with

        Returns:
            True if connection test succeeds
        """
        try:
            # Create temporary configuration
            temp_config = ProviderConfig(
                provider_type=provider_type,
                api_key=api_key.strip(),
                model=model.strip(),
                max_tokens=100,  # Small limit for testing
                temperature=0.1,
                timeout=10  # Short timeout for testing
            )

            # Create provider instance
            provider = ProviderFactory.create_provider(temp_config)

            # Test connection
            is_valid = await provider.validate_connection()

            if is_valid:
                logger.info(f"Provider {provider_type.value} test successful")
            else:
                logger.warning(f"Provider {provider_type.value} test failed")

            return is_valid

        except Exception as e:
            logger.error(f"Error testing provider {provider_type.value}: {e}")
            return False

    async def get_provider_models(self, provider_type: ProviderType) -> List[str]:
        """Get available models from a provider's API.

        Args:
            provider_type: The provider type

        Returns:
            List of available model names
        """
        if not self.is_provider_configured(provider_type):
            # Return default models if not configured
            return self.get_available_models(provider_type)

        try:
            # Get provider configuration
            config = self.get_provider_config(provider_type)
            if not config:
                return self.get_available_models(provider_type)

            # Create provider instance
            provider = ProviderFactory.create_provider(config)

            # Get models from API
            models = await provider.get_available_models()

            logger.debug(f"Retrieved {len(models)} models from {provider_type.value}")
            return models

        except Exception as e:
            logger.warning(f"Could not fetch models from {provider_type.value}: {e}")
            # Fall back to default models
            return self.get_available_models(provider_type)

    def create_current_provider(self):
        """Create an instance of the current provider.

        Returns:
            Provider instance for the current provider

        Raises:
            AppError: If no provider is configured or provider creation fails
        """
        current_provider = self.get_current_provider()
        if not current_provider:
            raise create_error(
                ErrorCode.PROVIDER_NOT_CONFIGURED,
                "No current provider configured"
            )

        config = self.get_provider_config(current_provider)
        if not config:
            raise create_error(
                ErrorCode.PROVIDER_NOT_CONFIGURED,
                f"Provider {current_provider.value} is not configured"
            )

        try:
            return ProviderFactory.create_provider(config)
        except Exception as e:
            raise create_error(
                ErrorCode.PROVIDER_CREATION_FAILED,
                f"Failed to create provider {current_provider.value}: {e}"
            )

    def create_provider_instance(self, provider_type: ProviderType):
        """Create an instance of a specific provider.

        Args:
            provider_type: The provider type to create

        Returns:
            Provider instance

        Raises:
            AppError: If provider is not configured or creation fails
        """
        if not self.is_provider_configured(provider_type):
            raise create_error(
                ErrorCode.PROVIDER_NOT_CONFIGURED,
                f"Provider {provider_type.value} is not configured"
            )

        config = self.get_provider_config(provider_type)
        if not config:
            raise create_error(
                ErrorCode.PROVIDER_NOT_CONFIGURED,
                f"Provider {provider_type.value} configuration not found"
            )

        try:
            return ProviderFactory.create_provider(config)
        except Exception as e:
            raise create_error(
                ErrorCode.PROVIDER_CREATION_FAILED,
                f"Failed to create provider {provider_type.value}: {e}"
            )
